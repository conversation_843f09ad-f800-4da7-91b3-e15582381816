using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    /// <summary>
    /// 静态岩石力学参数法脆性指数计算窗体
    /// </summary>
    public partial class StaticRockMechanicsForm : Form
    {
        #region 字段和属性

        private readonly string username = "";
        private DataTable mechanicsData = new DataTable();
        private DataTable? originalMechanicsData;
        private string? currentExcelFile;

        // 列名识别字典
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        #endregion

        #region 构造函数

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public StaticRockMechanicsForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎使用静态岩石力学参数法, {username}";
            }
        }

        #endregion

        #region 初始化方法

        private void InitializeForm()
        {
            // 初始化数据表
            InitializeDataTable();

            // 绑定事件
            Load += StaticRockMechanicsForm_Load;
            Resize += StaticRockMechanicsForm_Resize;
            FormClosing += StaticRockMechanicsForm_FormClosing;

            // 绑定单位选择事件（如果控件存在）
            BindUnitSelectionEvents();
        }

        private void InitializeDataTable()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("顶深/m", typeof(double));
            mechanicsData.Columns.Add("底深/m", typeof(double));
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            if (dgvMechanicsData != null)
            {
                dgvMechanicsData.DataSource = mechanicsData;
            }
        }

        /// <summary>
        /// 绑定单位选择控件的事件处理器
        /// </summary>
        private void BindUnitSelectionEvents()
        {
            try
            {
                // 查找并绑定密度单位选择控件
                var rbDensityRho = FindControlByName("rbDensityRho") as RadioButton;
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;

                // 查找并绑定纵波单位选择控件
                var rbVelocityVp = FindControlByName("rbVelocityVp") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;

                // 查找并绑定横波单位选择控件
                var rbVelocityVs = FindControlByName("rbVelocityVs") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 绑定事件处理器
                if (rbDensityRho != null) rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbDensityRhob != null) rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVp != null) rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDt != null) rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityVs != null) rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
                if (rbVelocityDts != null) rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                // 如果控件不存在，忽略错误（设计器中可能还未创建）
                System.Diagnostics.Debug.WriteLine($"绑定单位选择事件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归查找控件
        /// </summary>
        private Control? FindControlByName(string name)
        {
            return FindControlByName(this, name);
        }

        /// <summary>
        /// 递归查找指定名称的控件
        /// </summary>
        private Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        #endregion




        #region 事件处理方法

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            // 窗体加载时的初始化
            UpdateParameterLabels();
        }

        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时调整控件位置
            // 这里可以添加响应式布局代码，或者在设计器中使用Anchor和Dock属性
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        /// <summary>
        /// 单位选择变化事件处理器
        /// </summary>
        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                // 更新标签文本以反映当前选择的单位
                UpdateParameterLabels();
            }
        }

        /// <summary>
        /// 更新参数标签文本
        /// </summary>
        private void UpdateParameterLabels()
        {
            try
            {
                // 查找标签控件
                var lblDensity = FindControlByName("lblDensity") as Label;
                var lblVp = FindControlByName("lblVp") as Label;
                var lblVs = FindControlByName("lblVs") as Label;

                // 查找单位选择控件
                var rbDensityRhob = FindControlByName("rbDensityRhob") as RadioButton;
                var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;

                // 更新密度标签
                if (lblDensity != null)
                {
                    lblDensity.Text = rbDensityRhob?.Checked == true ? "岩石密度 RHOB (g/cm³):" : "岩石密度 ρ (g/cm³):";
                }

                // 更新纵波标签
                if (lblVp != null)
                {
                    lblVp.Text = rbVelocityDt?.Checked == true ? "纵波时差 DT (μs/m):" : "纵波速度 Vp (m/s):";
                }

                // 更新横波标签
                if (lblVs != null)
                {
                    lblVs.Text = rbVelocityDts?.Checked == true ? "横波时差 DTS (μs/m):" : "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新参数标签时出错: {ex.Message}");
            }
        }



        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有导入的数据需要批量计算
                if (mechanicsData != null && mechanicsData.Rows.Count > 0)
                {
                    // 批量计算模式
                    CalculateBatchData();
                }
                else
                {
                    // 单个计算模式
                    CalculateSingleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单个数据计算
        /// </summary>
        private void CalculateSingleData()
        {
            // 获取输入参数
            if (!double.TryParse(txtDensity.Text, out double inputDensity) ||
                !double.TryParse(txtVp.Text, out double inputVp) ||
                !double.TryParse(txtVs.Text, out double inputVs))
            {
                MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 进行单位转换，统一为标准单位
            double density = ConvertDensityToStandard(inputDensity);
            double vp = ConvertVelocityToStandard(inputVp, true); // true表示纵波
            double vs = ConvertVelocityToStandard(inputVs, false); // false表示横波

            // 根据您提供的公式计算静态岩石力学参数
            var result = CalculateStaticRockMechanics(density, vp, vs);

            // 显示计算结果
            lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

            // 添加到数据表
            DataRow newRow = mechanicsData.NewRow();
            newRow["顶深/m"] = 0.0; // 默认值，用户可以在导入数据时修改
            newRow["底深/m"] = 0.0; // 默认值
            newRow["密度/(g/cm³)"] = density;
            newRow["纵波速度/(m/s)"] = vp;
            newRow["横波速度/(m/s)"] = vs;
            newRow["动态杨氏模量/GPa"] = result.Ed;
            newRow["动态泊松比"] = result.MuD;
            newRow["静态杨氏模量/GPa"] = result.Es;
            newRow["静态泊松比"] = result.MuS;
            newRow["脆性指数/%"] = result.BrittlenessIndex;

            mechanicsData.Rows.Add(newRow);

            MessageBox.Show($"计算成功！脆性指数为: {result.BrittlenessIndex:F2}%", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 批量数据计算
        /// </summary>
        private void CalculateBatchData()
        {
            // 智能识别列名
            string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
            string? densityColumnName = FindColumnByPattern(mechanicsData, "密度");
            string? vpColumnName = FindColumnByPattern(mechanicsData, "纵波速度");
            string? vsColumnName = FindColumnByPattern(mechanicsData, "横波速度");

            // 检查必需的列是否存在
            if (string.IsNullOrEmpty(depthColumnName))
            {
                MessageBox.Show("未找到深度列，请检查数据格式！支持的深度列名包括：深度、depth、顶深、井深等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(densityColumnName))
            {
                MessageBox.Show("未找到密度列，请检查数据格式！支持的密度列名包括：密度、ρ、rho、rhob等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vpColumnName))
            {
                MessageBox.Show("未找到纵波速度列，请检查数据格式！支持的纵波速度列名包括：纵波速度、vp、dt等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (string.IsNullOrEmpty(vsColumnName))
            {
                MessageBox.Show("未找到横波速度列，请检查数据格式！支持的横波速度列名包括：横波速度、vs、dts等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 添加计算结果列（如果不存在）
            if (!mechanicsData.Columns.Contains("动态杨氏模量/GPa"))
                mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("动态泊松比"))
                mechanicsData.Columns.Add("动态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("静态杨氏模量/GPa"))
                mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            if (!mechanicsData.Columns.Contains("静态泊松比"))
                mechanicsData.Columns.Add("静态泊松比", typeof(double));
            if (!mechanicsData.Columns.Contains("脆性指数/%"))
                mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            int calculatedCount = 0;
            int errorCount = 0;

            // 批量计算每一行
            foreach (DataRow row in mechanicsData.Rows)
            {
                try
                {
                    // 获取数据
                    if (row[densityColumnName] == DBNull.Value || row[vpColumnName] == DBNull.Value || row[vsColumnName] == DBNull.Value)
                        continue;

                    if (!double.TryParse(row[densityColumnName].ToString(), out double density) ||
                        !double.TryParse(row[vpColumnName].ToString(), out double vp) ||
                        !double.TryParse(row[vsColumnName].ToString(), out double vs))
                    {
                        errorCount++;
                        continue;
                    }

                    // 进行单位转换
                    density = ConvertDensityToStandard(density);
                    vp = ConvertVelocityToStandard(vp, true);
                    vs = ConvertVelocityToStandard(vs, false);

                    // 计算岩石力学参数
                    var result = CalculateStaticRockMechanics(density, vp, vs);

                    // 保存计算结果
                    row["动态杨氏模量/GPa"] = result.Ed;
                    row["动态泊松比"] = result.MuD;
                    row["静态杨氏模量/GPa"] = result.Es;
                    row["静态泊松比"] = result.MuS;
                    row["脆性指数/%"] = result.BrittlenessIndex;

                    calculatedCount++;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"计算行数据时出错: {ex.Message}");
                    errorCount++;
                }
            }

            // 刷新数据显示
            dgvMechanicsData.Refresh();

            MessageBox.Show($"批量计算完成！成功计算 {calculatedCount} 行数据，{errorCount} 行数据计算失败。", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 将密度转换为标准单位 (g/cm³)
        /// </summary>
        private double ConvertDensityToStandard(double inputValue)
        {
            // ρ 和 RHOB 都是 g/cm³，无需转换
            return inputValue;
        }

        /// <summary>
        /// 将速度/时差转换为标准速度单位 (m/s)
        /// </summary>
        /// <param name="inputValue">输入值</param>
        /// <param name="isVp">是否为纵波，true=纵波，false=横波</param>
        /// <returns>转换后的速度值 (m/s)</returns>
        private double ConvertVelocityToStandard(double inputValue, bool isVp)
        {
            try
            {
                if (isVp)
                {
                    // 纵波：检查是否选择了DT
                    var rbVelocityDt = FindControlByName("rbVelocityDt") as RadioButton;
                    if (rbVelocityDt?.Checked == true)
                    {
                        // DT (μs/m) 转换为 Vp (m/s)
                        // Vp = 10^6 / DT
                        return 1000000.0 / inputValue;
                    }
                    else
                    {
                        // 已经是 Vp (m/s)，直接返回
                        return inputValue;
                    }
                }
                else
                {
                    // 横波：检查是否选择了DTS
                    var rbVelocityDts = FindControlByName("rbVelocityDts") as RadioButton;
                    if (rbVelocityDts?.Checked == true)
                    {
                        // DTS (μs/m) 转换为 Vs (m/s)
                        // Vs = 10^6 / DTS
                        return 1000000.0 / inputValue;
                    }
                    else
                    {
                        // 已经是 Vs (m/s)，直接返回
                        return inputValue;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"单位转换时出错: {ex.Message}");
                // 出错时直接返回输入值
                return inputValue;
            }
        }
        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mechanicsData = ds.Tables[0];
                            originalMechanicsData = mechanicsData.Copy();
                            dgvMechanicsData.DataSource = mechanicsData;

                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 智能识别列名
        /// </summary>
        private string? FindColumnByPattern(DataTable dataTable, string patternKey)
        {
            if (!columnPatterns.ContainsKey(patternKey))
                return null;

            var patterns = columnPatterns[patternKey];

            foreach (DataColumn column in dataTable.Columns)
            {
                string columnName = column.ColumnName.Trim().ToLower();
                foreach (string pattern in patterns)
                {
                    if (columnName.Contains(pattern.ToLower()))
                    {
                        return column.ColumnName;
                    }
                }
            }
            return null;
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 清除现有系列
                chartBrittleness.Series.Clear();

                // 智能识别列名
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                string? brittlenessColumnName = "脆性指数/%"; // 这是计算后的列名

                // 检查是否找到深度列
                if (string.IsNullOrEmpty(depthColumnName))
                {
                    MessageBox.Show("未找到深度列，请检查数据格式！支持的深度列名包括：深度、depth、顶深、井深等", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 检查是否存在脆性指数列
                if (!mechanicsData.Columns.Contains(brittlenessColumnName))
                {
                    MessageBox.Show("未找到脆性指数列，请先进行计算！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 创建脆性指数曲线系列 - 使用直线连接
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Line, // 使用直线而不是Spline
                    Color = Color.Cyan,
                    BorderWidth = 2,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerSize = 6,
                    MarkerColor = Color.Yellow
                };

                // 添加数据点
                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row[brittlenessColumnName] != DBNull.Value && row[depthColumnName] != DBNull.Value)
                    {
                        if (double.TryParse(row[brittlenessColumnName].ToString(), out double brittleness) &&
                            double.TryParse(row[depthColumnName].ToString(), out double depth))
                        {
                            series.Points.AddXY(brittleness, depth);
                        }
                    }
                }

                if (series.Points.Count == 0)
                {
                    MessageBox.Show("没有有效的数据点可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                chartBrittleness.Series.Add(series);

                // 设置图表区域属性
                var chartArea = chartBrittleness.ChartAreas[0];

                // 设置Y轴（深度）反向显示
                chartArea.AxisY.IsReversed = true;

                // 设置轴范围
                var depths = series.Points.Select(p => p.YValues[0]).ToList();
                var brittlenessValues = series.Points.Select(p => p.XValue).ToList();

                chartArea.AxisY.Minimum = depths.Min();
                chartArea.AxisY.Maximum = depths.Max();
                chartArea.AxisX.Minimum = Math.Max(0, brittlenessValues.Min() - 5);
                chartArea.AxisX.Maximum = brittlenessValues.Max() + 5;

                // 设置轴标题
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";

                // 设置网格线
                chartArea.AxisX.MajorGrid.Enabled = true;
                chartArea.AxisY.MajorGrid.Enabled = true;
                chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
                chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;

                MessageBox.Show("曲线生成成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空输入框
                txtDensity.Text = "";
                txtVp.Text = "";
                txtVs.Text = "";
                lblCalculationResult.Text = "计算结果将在此显示";

                // 清空图表
                chartBrittleness.Series.Clear();

                // 重置数据表
                if (originalMechanicsData != null)
                {
                    mechanicsData = originalMechanicsData.Copy();
                    dgvMechanicsData.DataSource = mechanicsData;
                }
                else
                {
                    InitializeDataTable();
                }

                MessageBox.Show("已重置所有数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("没有曲线可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "PNG图片 (*.png)|*.png|JPEG图片 (*.jpg)|*.jpg";
                saveFileDialog.Title = "保存曲线图片";
                saveFileDialog.FileName = $"静态岩石力学参数脆性指数曲线_{DateTime.Now:yyyyMMdd}.png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    chartBrittleness.SaveImage(saveFileDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show("曲线图片保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 计算方法

        /// <summary>
        /// 计算结果结构体
        /// </summary>
        public struct RockMechanicsResult
        {
            public double Ed;              // 动态杨氏模量 (GPa)
            public double MuD;             // 动态泊松比
            public double Es;              // 静态杨氏模量 (GPa)
            public double MuS;             // 静态泊松比
            public double BrittlenessIndex; // 脆性指数 (%)
        }

        /// <summary>
        /// 根据您提供的公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 转换密度单位：g/cm³ -> kg/m³
            double rho = density * 1000;

            // 计算动态杨氏模量 Ed (GPa)
            // Ed = 10^-3 * ρ * Vs^2 * (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            result.Ed = 1e-3 * rho * vs2 * (3 * vp2 - 4 * vs2) / (vp2 - vs2) / 1e9; // 转换为GPa

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * (vp2 - vs2));

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 计算脆性指数
            // 需要先计算归一化的杨氏模量和泊松比
            // 这里使用常见的岩石参数范围进行归一化
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = (result.Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内

            // 归一化泊松比脆性指数
            // μBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double MuBRIT = (MuSMax - result.MuS) / (MuSMax - MuSMin) * 100;
            MuBRIT = Math.Max(0, Math.Min(100, MuBRIT)); // 限制在0-100%范围内

            // 综合脆性指数
            // BRITe = (EBRIT + μBRIT) / 2
            result.BrittlenessIndex = (EBRIT + MuBRIT) / 2;

            return result;
        }

        #endregion
        #region Excel读写方法

        /// <summary>
        /// 读取Excel文件
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败: {ex.Message}");
            }
        }
        /// <summary>
        /// 导出Excel文件
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    IWorkbook workbook = new XSSFWorkbook();
                    ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                    // 创建表头
                    IRow headerRow = sheet.CreateRow(0);
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                    }

                    // 创建数据行
                    for (int i = 0; i < mechanicsData.Rows.Count; i++)
                    {
                        IRow dataRow = sheet.CreateRow(i + 1);
                        for (int j = 0; j < mechanicsData.Columns.Count; j++)
                        {
                            var cellValue = mechanicsData.Rows[i][j];
                            if (cellValue != DBNull.Value)
                            {
                                if (double.TryParse(cellValue.ToString(), out double numValue))
                                {
                                    dataRow.CreateCell(j).SetCellValue(numValue);
                                }
                                else
                                {
                                    dataRow.CreateCell(j).SetCellValue(cellValue.ToString());
                                }
                            }
                        }
                    }

                    // 自动调整列宽
                    for (int i = 0; i < mechanicsData.Columns.Count; i++)
                    {
                        sheet.AutoSizeColumn(i);
                    }

                    workbook.Write(stream);
                }

                MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel文件失败: {ex.Message}");
            }
        }

        #endregion
    }
}
