﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using System.IO;
using System.Diagnostics;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace BritSystem
{
    public partial class MineralogicalForm : Form
    {
        // 静态数据表，用于存储脆性指数计算器返回的数据
        private static DataTable _brittlenessData = null;

        // 加载脆性指数数据的静态方法
        public static void LoadBrittlenessData(DataTable data)
        {
            _brittlenessData = data;
        }

        // 数据点类
        private class DataPoint
        {
            public Guid Id { get; } = Guid.NewGuid();
            public string GeoID { get; set; } // 地质点唯一标识
            public double TopDepth { get; set; }
            public double BottomDepth { get; set; }
            public double BrittleIndex { get; set; }
            public int RowIndex { get; set; }

            // 生成唯一的GeoID
            public void GenerateGeoID()
            {
                // 使用深度和脆性指数生成唯一的GeoID
                GeoID = $"GEO_{TopDepth:F2}_{BottomDepth:F2}_{BrittleIndex:F4}_{Guid.NewGuid().ToString().Substring(0, 8)}";
                System.Diagnostics.Debug.WriteLine($"生成GeoID: {GeoID}");
            }
        }

        // 配置类和控件声明
        private class AppConfig
        {
            public int MaxHeaderSearchRows { get; } = 20;
            public string[] RequiredColumns { get; } = { "顶深", "底深", "脆性" };
        }
        private readonly AppConfig config = new AppConfig();

        // 字段
        private List<DataPoint> dataPoints = new List<DataPoint>();
        private List<double> yAxisLabels = new List<double>();
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private List<int> selectedRows = new List<int>();
        private string lastMatchedGeoID = null; // 存储最后匹配的GeoID
        private string username;
        private BackgroundWorker dataLoader;
        private DataTable mineralData;
        private DataTable originalMineralData; // 保存原始数据，用于还原
        private System.Windows.Forms.DataVisualization.Charting.Chart chartBrittleness;
        private string currentExcelFile;
        private List<int> deletedRows = new List<int>();
        private Button btnSearch;
        private Button btnResetData;

        // 鼠标悬停相关字段
        private int? hoveredPointIndex = null; // 当前鼠标悬停的点索引
        private int originalMarkerSize = 8; // 原始点大小
        private Color originalPointColor = Color.Red; // 原始点颜色
        private Color hoverPointColor = Color.Purple; // 悬停时的点颜色

        public MineralogicalForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public MineralogicalForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeForm();

            // 设置欢迎文本
            if (lblWelcome != null)
            {
                lblWelcome.Text = $"欢迎, {username}";
            }
        }

        // 数据表右键菜单
        private ContextMenuStrip dgvContextMenu;

        private void InitializeForm()
        {
            // 初始化异步处理
            dataLoader = new BackgroundWorker { WorkerReportsProgress = true, WorkerSupportsCancellation = true };

            // 设置控件的锚点属性
            SetControlAnchors();

            // 绑定事件
            this.Load += MineralogicalForm_Load;
            this.Resize += MineralogicalForm_Resize;
            this.FormClosing += MineralogicalForm_FormClosing;

            btnCalculate.Click += BtnCalculate_Click;
            btnImport.Click += BtnImport_Click;
            btnExport.Click += BtnExport_Click;
            btnGenerateCurve.Click += BtnGenerateCurve_Click;
            btnDeletePoint.Click += BtnDeletePoint_Click;
            btnReset.Click += BtnReset_Click;
            btnSaveCurve.Click += BtnSaveCurve_Click;
            btnBack.Click += BtnBack_Click;
            btnLogout.Click += BtnLogout_Click;
            btnEmergencyExit.Click += BtnEmergencyExit_Click;

            // 数据网格事件
            dgvMineralData.SelectionChanged += DataGridView_SelectionChanged;
            dgvMineralData.CellClick += DataGridView_CellClick;
            dgvMineralData.CellFormatting += DataGridView_CellFormatting;

            // 初始化右键菜单
            InitializeContextMenu();

            // 图表事件
            if (chartBrittleness != null)
            {
                chartBrittleness.MouseWheel += MineralChart_MouseWheel;
                chartBrittleness.MouseMove += MineralChart_MouseMove;
                chartBrittleness.MouseDown += MineralChart_MouseDown;
                chartBrittleness.MouseUp += MineralChart_MouseUp;
                chartBrittleness.MouseClick += MineralChart_MouseClick;
                chartBrittleness.MouseEnter += MineralChart_MouseEnter;
                chartBrittleness.MouseLeave += MineralChart_MouseLeave;

                // 使图表可以获取焦点
                chartBrittleness.TabStop = true;
                chartBrittleness.TabIndex = 0;
            }

            // 添加窗体键盘事件处理
            this.KeyPreview = true;
            this.KeyDown += MineralogicalForm_KeyDown;

            // 初始化数据表
            InitializeDataTable();

            // 初始化搜索控件
            InitializeSearchControls();
        }

        private void InitializeContextMenu()
        {
            // 创建右键菜单
            dgvContextMenu = new ContextMenuStrip();

            // 添加"删除点"菜单项
            ToolStripMenuItem deleteMenuItem = new ToolStripMenuItem("删除点");
            deleteMenuItem.Click += DeleteMenuItem_Click;
            dgvContextMenu.Items.Add(deleteMenuItem);

            // 将右键菜单关联到数据网格
            dgvMineralData.ContextMenuStrip = dgvContextMenu;

            // 添加右键菜单显示前的事件处理
            dgvContextMenu.Opening += DgvContextMenu_Opening;
        }

        private void DgvContextMenu_Opening(object sender, CancelEventArgs e)
        {
            // 如果没有选中行，禁用"删除点"菜单项
            if (selectedRows.Count == 0)
            {
                e.Cancel = true; // 取消显示右键菜单
            }
        }

        private void DeleteMenuItem_Click(object sender, EventArgs e)
        {
            // 调用删除点方法
            BtnDeletePoint_Click(sender, e);
        }

        private void MineralogicalForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 取消正在运行的后台任务
                if (dataLoader != null && dataLoader.IsBusy)
                {
                    dataLoader.CancelAsync();
                }

                // 释放图表资源
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }

                // 强制关闭窗体
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                // 即使出错也强制关闭
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }

        private void MineralogicalForm_Load(object sender, EventArgs e)
        {
            // 设置图表样式
            SetupChartStyle();

            // 初始化时手动调用一次Resize事件处理方法，确保控件大小正确
            MineralogicalForm_Resize(this, EventArgs.Empty);

            // 检查是否有脆性指数计算器返回的数据
            if (_brittlenessData != null && _brittlenessData.Rows.Count > 0)
            {
                try
                {
                    // 将脆性指数数据加载到矿物数据表中
                    LoadBrittlenessDataToMineralData();

                    // 保存原始数据的副本，用于还原
                    if (mineralData != null)
                    {
                        originalMineralData = mineralData.Copy();

                        // 更新搜索下拉框中的列名
                        UpdateSearchColumnComboBox();
                    }

                    // 生成曲线
                    BtnGenerateCurve_Click(this, EventArgs.Empty);

                    // 清空静态数据表，防止重复加载
                    _brittlenessData = null;

                    // 显示成功消息
                    MessageBox.Show("脆性指数数据已成功加载并生成曲线！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"加载脆性指数数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        // 加载脆性指数数据到矿物数据表
        private void LoadBrittlenessDataToMineralData()
        {
            if (_brittlenessData == null || _brittlenessData.Rows.Count == 0)
                return;

            // 确保矿物数据表已初始化
            if (mineralData == null)
            {
                mineralData = new DataTable();
                mineralData.Columns.Add("顶深/m", typeof(double));
                mineralData.Columns.Add("底深/m", typeof(double));
                mineralData.Columns.Add("脆性指数", typeof(double));
            }

            // 将脆性指数数据复制到矿物数据表
            foreach (DataRow row in _brittlenessData.Rows)
            {
                DataRow newRow = mineralData.NewRow();
                foreach (DataColumn col in _brittlenessData.Columns)
                {
                    if (mineralData.Columns.Contains(col.ColumnName))
                    {
                        newRow[col.ColumnName] = row[col.ColumnName];
                    }
                }
                mineralData.Rows.Add(newRow);
            }

            // 更新数据网格
            dgvMineralData.DataSource = mineralData;
        }

        // 设置控件的锚点属性
        private void SetControlAnchors()
        {
            // 设置pnlInput面板中控件的锚点属性
            // 左侧控件（矿物组分输入区域）
            lblInputTitle.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblQuartz.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtQuartz.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblFeldspar.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtFeldspar.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblCarbonate.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtCarbonate.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblClay.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtClay.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            btnCalculate.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblResult.Anchor = AnchorStyles.Top | AnchorStyles.Left;

            // 分割线 - 固定位置，高度随面板高度变化
            pnlDivider.Anchor = AnchorStyles.Top | AnchorStyles.Bottom;

            // 右侧控件（数据搜索区域）
            lblSearchTitle.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblSearchColumn.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            cboSearchColumn.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblMinValue.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtMinValue.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblMaxValue.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            txtMaxValue.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            btnSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnResetData.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // 设置其他面板的锚点属性
            pnlInput.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pnlData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left;
            pnlChart.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // 设置数据网格的锚点属性
            dgvMineralData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // 设置图表的锚点属性
            if (chartBrittleness != null)
            {
                chartBrittleness.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            }
        }

        private void MineralogicalForm_Resize(object sender, EventArgs e)
        {
            // 使用自适应布局，根据窗口大小调整控件

            // 计算窗口宽度和高度的比例
            float widthRatio = (float)this.ClientSize.Width / 1430f; // 原始设计宽度
            float heightRatio = (float)this.ClientSize.Height / 950f; // 原始设计高度

            // 紧急退出按钮位置
            btnEmergencyExit.Location = new Point((int)(this.ClientSize.Width - 125 * widthRatio), (int)(12 * heightRatio));
            btnEmergencyExit.Size = new Size((int)(115 * widthRatio), (int)(37 * heightRatio));

            // 标题和欢迎文本
            lblTitle.Size = new Size(this.ClientSize.Width, (int)(97 * heightRatio));
            lblTitle.Location = new Point(0, 0);

            // 欢迎文本
            lblWelcome.Location = new Point((int)(20 * widthRatio), (int)(60 * heightRatio));
            lblWelcome.Size = new Size((int)(300 * widthRatio), (int)(30 * heightRatio));

            // 返回和退出按钮
            btnBack.Location = new Point((int)(20 * widthRatio), (int)(100 * heightRatio));
            btnBack.Size = new Size((int)(150 * widthRatio), (int)(40 * heightRatio));

            btnLogout.Location = new Point((int)(this.ClientSize.Width - 170 * widthRatio), (int)(100 * heightRatio));
            btnLogout.Size = new Size((int)(150 * widthRatio), (int)(40 * heightRatio));

            // 脆性指数计算器按钮
            btnAlgFormulaCal.Location = new Point((int)(661 * widthRatio), (int)(100 * heightRatio));
            btnAlgFormulaCal.Size = new Size((int)(150 * widthRatio), (int)(40 * heightRatio));

            // 输入面板
            pnlInput.Location = new Point((int)(20 * widthRatio), (int)(160 * heightRatio));
            pnlInput.Size = new Size((int)((this.ClientSize.Width - 40) * widthRatio), (int)(131 * heightRatio));

            // 调整pnlInput面板中控件的位置和大小
            // 左侧控件（矿物组分输入区域）
            lblInputTitle.Location = new Point((int)(6 * widthRatio), (int)(0 * heightRatio));
            lblInputTitle.Size = new Size((int)(200 * widthRatio), (int)(35 * heightRatio));

            lblQuartz.Location = new Point((int)(10 * widthRatio), (int)(45 * heightRatio));
            lblQuartz.Size = new Size((int)(80 * widthRatio), (int)(34 * heightRatio));
            txtQuartz.Location = new Point((int)(90 * widthRatio), (int)(45 * heightRatio));
            txtQuartz.Size = new Size((int)(67 * widthRatio), (int)(34 * heightRatio));

            lblFeldspar.Location = new Point((int)(180 * widthRatio), (int)(46 * heightRatio));
            lblFeldspar.Size = new Size((int)(62 * widthRatio), (int)(25 * heightRatio));
            txtFeldspar.Location = new Point((int)(254 * widthRatio), (int)(45 * heightRatio));
            txtFeldspar.Size = new Size((int)(73 * widthRatio), (int)(34 * heightRatio));

            lblCarbonate.Location = new Point((int)(347 * widthRatio), (int)(45 * heightRatio));
            lblCarbonate.Size = new Size((int)(80 * widthRatio), (int)(25 * heightRatio));
            txtCarbonate.Location = new Point((int)(428 * widthRatio), (int)(45 * heightRatio));
            txtCarbonate.Size = new Size((int)(80 * widthRatio), (int)(34 * heightRatio));

            lblClay.Location = new Point((int)(516 * widthRatio), (int)(46 * heightRatio));
            lblClay.Size = new Size((int)(68 * widthRatio), (int)(25 * heightRatio));
            txtClay.Location = new Point((int)(590 * widthRatio), (int)(45 * heightRatio));
            txtClay.Size = new Size((int)(80 * widthRatio), (int)(34 * heightRatio));

            btnCalculate.Location = new Point((int)(20 * widthRatio), (int)(89 * heightRatio));
            btnCalculate.Size = new Size((int)(150 * widthRatio), (int)(35 * heightRatio));

            lblResult.Location = new Point((int)(176 * widthRatio), (int)(91 * heightRatio));
            lblResult.Size = new Size((int)(251 * widthRatio), (int)(35 * heightRatio));

            // 分割线 - 保持设计器中的位置
            pnlDivider.Location = new Point((int)(680 * widthRatio), 0);
            pnlDivider.Size = new Size(2, pnlInput.Height);

            // 右侧控件（数据搜索区域）
            lblSearchTitle.Location = new Point((int)(695 * widthRatio), (int)(5 * heightRatio));
            lblSearchTitle.Size = new Size((int)(124 * widthRatio), (int)(35 * heightRatio));

            lblSearchColumn.Location = new Point((int)(702 * widthRatio), (int)(49 * heightRatio));
            lblSearchColumn.Size = new Size((int)(77 * widthRatio), (int)(27 * heightRatio));
            cboSearchColumn.Location = new Point((int)(791 * widthRatio), (int)(47 * heightRatio));
            cboSearchColumn.Size = new Size((int)(120 * widthRatio), (int)(32 * heightRatio));

            lblMinValue.Location = new Point((int)(951 * widthRatio), (int)(47 * heightRatio));
            lblMinValue.Size = new Size((int)(77 * widthRatio), (int)(27 * heightRatio));
            txtMinValue.Location = new Point((int)(1044 * widthRatio), (int)(45 * heightRatio));
            txtMinValue.Size = new Size((int)(117 * widthRatio), (int)(30 * heightRatio));

            lblMaxValue.Location = new Point((int)(1189 * widthRatio), (int)(47 * heightRatio));
            lblMaxValue.Size = new Size((int)(77 * widthRatio), (int)(27 * heightRatio));
            txtMaxValue.Location = new Point((int)(1282 * widthRatio), (int)(45 * heightRatio));
            txtMaxValue.Size = new Size((int)(106 * widthRatio), (int)(30 * heightRatio));

            // 搜索和还原按钮 - 使用面板宽度计算位置，确保它们始终在右侧
            btnSearch.Size = new Size((int)(116 * widthRatio), (int)(35 * heightRatio));
            btnResetData.Size = new Size((int)(118 * widthRatio), (int)(35 * heightRatio));

            int searchX = pnlInput.Width - (int)(132 * widthRatio);
            int resetX = pnlInput.Width - (int)(356 * widthRatio);
            btnSearch.Location = new Point(searchX, (int)(89 * heightRatio));
            btnResetData.Location = new Point(resetX, (int)(89 * heightRatio));

            // 数据和图表面板的位置
            int panelsTop = (int)(297 * heightRatio);
            int dataWidth = (int)(577 * widthRatio);
            int chartWidth = (int)(807 * widthRatio);
            int panelHeight = (int)(620 * heightRatio);

            // 数据面板
            pnlData.Location = new Point((int)(20 * widthRatio), panelsTop);
            pnlData.Size = new Size(dataWidth, panelHeight);

            // 数据网格
            dgvMineralData.Location = new Point((int)(10 * widthRatio), (int)(62 * heightRatio));
            dgvMineralData.Size = new Size((int)(539 * widthRatio), (int)(516 * heightRatio));

            // 图表面板
            pnlChart.Location = new Point((int)(603 * widthRatio), panelsTop);
            pnlChart.Size = new Size(chartWidth, panelHeight);

            // 图表控件 - 留出滚动条空间
            if (chartBrittleness != null)
            {
                chartBrittleness.Location = new Point((int)(10 * widthRatio), (int)(50 * heightRatio));
                chartBrittleness.Size = new Size(pnlChart.Width - (int)(30 * widthRatio), pnlChart.Height - (int)(70 * heightRatio));
                chartBrittleness.Padding = new Padding(10, 10, 20, 20); // 增加内边距，为滚动条留出空间

                // 确保滚动条设置正确
                if (chartBrittleness.ChartAreas.Count > 0)
                {
                    var chartArea = chartBrittleness.ChartAreas[0];
                    chartArea.AxisY.ScrollBar.IsPositionedInside = false;
                    chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                    chartArea.AxisY.ScrollBar.Size = 15;
                    chartArea.AxisX.ScrollBar.Size = 15;
                    chartArea.AxisY.ScrollBar.Enabled = true;
                    chartArea.AxisX.ScrollBar.Enabled = true;

                    // 设置滚动条属性
                    // 注意：SmallScrollSize 和 SmallScrollMinSize 在当前版本的库中不可用

                    // 设置滚动条样式
                    chartArea.AxisY.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;
                    chartArea.AxisX.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;

                    // 设置滚动条颜色
                    chartArea.AxisY.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                    chartArea.AxisX.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                    chartArea.AxisY.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);
                    chartArea.AxisX.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);
                }
            }

            // 数据面板标题和按钮
            lblDataTitle.Location = new Point((int)(10 * widthRatio), (int)(5 * heightRatio));
            lblDataTitle.Size = new Size((int)(200 * widthRatio), (int)(37 * heightRatio));

            btnImport.Size = new Size((int)(110 * widthRatio), (int)(34 * heightRatio));
            btnExport.Size = new Size((int)(110 * widthRatio), (int)(37 * heightRatio));

            // 根据面板宽度调整按钮位置
            int importX = pnlData.Width - (int)(283 * widthRatio);
            int exportX = pnlData.Width - (int)(138 * widthRatio);
            btnImport.Location = new Point(importX, (int)(5 * heightRatio));
            btnExport.Location = new Point(exportX, (int)(5 * heightRatio));

            // 图表面板标题和按钮
            lblChartTitle.Location = new Point((int)(10 * widthRatio), (int)(5 * heightRatio));
            lblChartTitle.Size = new Size((int)(165 * widthRatio), (int)(37 * heightRatio));

            btnGenerateCurve.Size = new Size((int)(110 * widthRatio), (int)(36 * heightRatio));
            btnDeletePoint.Size = new Size((int)(110 * widthRatio), (int)(36 * heightRatio));
            btnReset.Size = new Size((int)(110 * widthRatio), (int)(36 * heightRatio));
            btnSaveCurve.Size = new Size((int)(110 * widthRatio), (int)(36 * heightRatio));

            // 根据面板宽度调整按钮位置
            int buttonSpacing = (int)(30 * widthRatio);
            int buttonWidth = (int)(110 * widthRatio);
            int rightMargin = (int)(10 * widthRatio);

            btnSaveCurve.Location = new Point(pnlChart.Width - 4 * buttonWidth - 3 * buttonSpacing - rightMargin, (int)(6 * heightRatio));
            btnReset.Location = new Point(pnlChart.Width - 3 * buttonWidth - 2 * buttonSpacing - rightMargin, (int)(6 * heightRatio));
            btnDeletePoint.Location = new Point(pnlChart.Width - 2 * buttonWidth - buttonSpacing - rightMargin, (int)(6 * heightRatio));
            btnGenerateCurve.Location = new Point(pnlChart.Width - buttonWidth - rightMargin, (int)(6 * heightRatio));

            // 强制重绘
            this.Invalidate();
        }

        private void InitializeDataTable()
        {
            // 创建空数据表，不预加载表头
            mineralData = new DataTable();

            // 绑定数据源
            dgvMineralData.DataSource = mineralData;

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine($"LoadData: 初始化空数据表完成");
        }

        private void InitializeSearchControls()
        {
            try
            {
                // 不再需要在窗口大小变化时调整控件位置
                // 控件位置已在设计器中设置好
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化搜索控件时出错: {ex.Message}");
                MessageBox.Show($"初始化搜索控件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 由于控件位置已在设计器中调整好，不再需要通过代码调整位置
        private void UpdateSearchControlsPosition()
        {
            // 此方法保留为空，以便将来可能需要的调整
        }

        private void LoadData()
        {
            // 创建数据表
            if (mineralData == null)
            {
                mineralData = new DataTable();
                mineralData.Columns.Add("顶深/m", typeof(double));
                mineralData.Columns.Add("底深/m", typeof(double));
                mineralData.Columns.Add("脆性指数", typeof(double));

                // 绑定数据源
                dgvMineralData.DataSource = mineralData;
            }
        }

        private void SetupChartStyle()
        {
            // 初始化图表对象
            if (chartBrittleness == null)
            {
                chartBrittleness = new System.Windows.Forms.DataVisualization.Charting.Chart();
                chartBrittleness.ChartAreas.Add(new ChartArea("Default"));

                // 添加到图表面板
                pnlChart.Controls.Add(chartBrittleness);

                // 设置图表位置和大小 - 留出滚动条空间
                chartBrittleness.Location = new Point(10, 50);
                chartBrittleness.Size = new Size(pnlChart.Width - 30, pnlChart.Height - 70); // 减小尺寸以适应滚动条
                chartBrittleness.Dock = DockStyle.None;
                chartBrittleness.BackColor = Color.FromArgb(45, 45, 45);
                chartBrittleness.ForeColor = Color.White;
                chartBrittleness.BorderlineColor = Color.FromArgb(60, 60, 60);
                chartBrittleness.BorderlineDashStyle = ChartDashStyle.Solid;
                chartBrittleness.BorderlineWidth = 1;
                chartBrittleness.Padding = new Padding(10, 10, 20, 20); // 增加内边距，为滚动条留出空间

                // 绑定图表事件
                chartBrittleness.MouseWheel += MineralChart_MouseWheel;
                chartBrittleness.MouseMove += MineralChart_MouseMove;
                chartBrittleness.MouseDown += MineralChart_MouseDown;
                chartBrittleness.MouseUp += MineralChart_MouseUp;
                chartBrittleness.MouseClick += MineralChart_MouseClick;
                chartBrittleness.MouseEnter += MineralChart_MouseEnter;
                chartBrittleness.MouseLeave += MineralChart_MouseLeave;

                // 设置默认光标
                chartBrittleness.Cursor = Cursors.Cross;
            }

            // 设置图表样式
            chartBrittleness.ChartAreas[0].AxisX.LabelStyle.Format = "0.00";
            chartBrittleness.ChartAreas[0].AxisY.LabelStyle.Format = "0.00";
            chartBrittleness.ChartAreas[0].AxisX.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.ChartAreas[0].AxisY.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.ChartAreas[0].AxisX.MajorGrid.LineDashStyle = ChartDashStyle.Dash;
            chartBrittleness.ChartAreas[0].AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

            // 设置图表区域样式
            chartBrittleness.ChartAreas[0].BackColor = Color.FromArgb(45, 45, 45);
            chartBrittleness.ChartAreas[0].AxisX.LabelStyle.ForeColor = Color.White;
            chartBrittleness.ChartAreas[0].AxisY.LabelStyle.ForeColor = Color.White;
            chartBrittleness.ChartAreas[0].AxisX.LineColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.ChartAreas[0].AxisY.LineColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.ChartAreas[0].AxisX.TitleForeColor = Color.White;
            chartBrittleness.ChartAreas[0].AxisY.TitleForeColor = Color.White;
            chartBrittleness.ChartAreas[0].BorderColor = Color.FromArgb(60, 60, 60);

            // 清除图表标题
            chartBrittleness.Titles.Clear();

            // 设置轴标题
            chartBrittleness.ChartAreas[0].AxisX.Title = "脆性指数";
            chartBrittleness.ChartAreas[0].AxisY.Title = "深度 (m)";
            chartBrittleness.ChartAreas[0].AxisX.TitleFont = new Font("微软雅黑", 10);
            chartBrittleness.ChartAreas[0].AxisY.TitleFont = new Font("微软雅黑", 10);
            chartBrittleness.ChartAreas[0].AxisX.TitleForeColor = Color.White;
            chartBrittleness.ChartAreas[0].AxisY.TitleForeColor = Color.White;

            // 设置图例样式
            chartBrittleness.Legends.Clear();
            var legend = new Legend()
            {
                BackColor = Color.FromArgb(45, 45, 45),
                ForeColor = Color.White,
                Font = new Font("微软雅黑", 9),
                Docking = Docking.Bottom,
                Alignment = StringAlignment.Center,
                BorderColor = Color.FromArgb(60, 60, 60),
                BorderWidth = 1
            };
            chartBrittleness.Legends.Add(legend);
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取输入值
                if (!double.TryParse(txtQuartz.Text, out double quartz) ||
                    !double.TryParse(txtFeldspar.Text, out double feldspar) ||
                    !double.TryParse(txtCarbonate.Text, out double carbonate) ||
                    !double.TryParse(txtClay.Text, out double clay))
                {
                    MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 计算脆性指数
                double brittleness = (quartz + feldspar + carbonate) / (quartz + feldspar + carbonate + clay) * 100;
                brittleness = Math.Round(brittleness, 2);

                // 显示结果
                lblResult.Text = $"脆性指数: {brittleness}%";

                // 检查数据表是否有列
                if (mineralData.Columns.Count == 0)
                {
                    // 添加必要的列
                    mineralData.Columns.Add("顶深/m", typeof(double));
                    mineralData.Columns.Add("底深/m", typeof(double));
                    mineralData.Columns.Add("脆性指数", typeof(double));
                }

                // 只有当脆性指数不为0时才添加到数据表中
                if (brittleness != 0)
                {
                    DataRow newRow = mineralData.NewRow();
                    newRow["顶深/m"] = 0.0; // 默认值
                    newRow["底深/m"] = 0.0; // 默认值
                    newRow["脆性指数"] = brittleness;
                    mineralData.Rows.Add(newRow);
                }

                // 不自动生成曲线，只显示计算结果
                MessageBox.Show($"计算成功! 脆性指数为: {brittleness}%", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 字典用于存储列位置信息
        private Dictionary<string, (int row, int col)> columnPositions = new Dictionary<string, (int row, int col)>();

        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // 先初始化数据表，确保数据表结构正确
                        LoadData();

                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mineralData = ds.Tables[0];

                            // 保存原始数据的副本，用于还原
                            originalMineralData = mineralData.Copy();

                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 自动识别列头
                            AutoDetectColumns();

                            // 更新搜索下拉框中的列名
                            UpdateSearchColumnComboBox();
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        // 更新搜索下拉框中的列名
        private void UpdateSearchColumnComboBox()
        {
            try
            {
                // 清空下拉框
                cboSearchColumn.Items.Clear();

                // 获取当前显示在dgvMineralData中的数据表
                DataTable currentDataTable = null;
                if (dgvMineralData.DataSource is DataTable)
                {
                    currentDataTable = (DataTable)dgvMineralData.DataSource;
                }

                // 如果数据表为空，则返回
                if (currentDataTable == null || currentDataTable.Columns.Count == 0)
                    return;

                // 添加所有列名到下拉框
                foreach (DataColumn column in currentDataTable.Columns)
                {
                    cboSearchColumn.Items.Add(column.ColumnName);
                }

                // 如果有列，则默认选择第一列
                if (cboSearchColumn.Items.Count > 0)
                    cboSearchColumn.SelectedIndex = 0;

                System.Diagnostics.Debug.WriteLine($"已更新搜索下拉框，共 {cboSearchColumn.Items.Count} 个列");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新搜索下拉框时出错: {ex.Message}");
            }
        }

        // 搜索按钮点击事件
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (cboSearchColumn.SelectedItem == null)
                {
                    MessageBox.Show("请选择要搜索的列", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMinValue.Text) || string.IsNullOrWhiteSpace(txtMaxValue.Text))
                {
                    MessageBox.Show("请输入最小值和最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 尝试解析最小值和最大值
                if (!double.TryParse(txtMinValue.Text, out double minValue) || !double.TryParse(txtMaxValue.Text, out double maxValue))
                {
                    MessageBox.Show("请输入有效的数值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保最小值小于等于最大值
                if (minValue > maxValue)
                {
                    MessageBox.Show("最小值不能大于最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取选择的列名
                string columnName = cboSearchColumn.SelectedItem.ToString();

                // 获取当前显示在dgvMineralData中的数据表
                DataTable currentDataTable = null;
                if (dgvMineralData.DataSource is DataTable)
                {
                    currentDataTable = (DataTable)dgvMineralData.DataSource;
                }
                else
                {
                    MessageBox.Show("无法获取当前数据", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 确保原始数据已保存
                if (originalMineralData == null)
                {
                    originalMineralData = currentDataTable.Copy();
                }

                // 创建筛选后的数据表
                DataTable filteredData = currentDataTable.Clone();

                // 根据条件筛选数据
                foreach (DataRow row in currentDataTable.Rows)
                {
                    // 跳过空值
                    if (row[columnName] == DBNull.Value)
                        continue;

                    // 尝试将值转换为double
                    if (double.TryParse(row[columnName].ToString(), out double value))
                    {
                        // 如果值在范围内，则添加到筛选后的数据表
                        if (value >= minValue && value <= maxValue)
                        {
                            filteredData.ImportRow(row);
                        }
                    }
                }

                // 如果没有符合条件的数据
                if (filteredData.Rows.Count == 0)
                {
                    MessageBox.Show("没有找到符合条件的数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 更新数据网格
                dgvMineralData.DataSource = filteredData;

                // 更新图表
                BtnGenerateCurve_Click(this, EventArgs.Empty);

                // 显示搜索结果
                MessageBox.Show($"找到 {filteredData.Rows.Count} 条符合条件的数据", "搜索结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 还原数据按钮点击事件
        private void BtnResetData_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果原始数据为空，则返回
                if (originalMineralData == null || originalMineralData.Rows.Count == 0)
                {
                    MessageBox.Show("没有原始数据可还原", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 还原数据网格
                dgvMineralData.DataSource = originalMineralData;

                // 更新图表
                BtnGenerateCurve_Click(this, EventArgs.Empty);

                // 清空搜索条件
                txtMinValue.Text = "";
                txtMaxValue.Text = "";

                // 显示还原成功消息
                MessageBox.Show("已还原原始数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"还原数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有数据可导出
                if (dgvMineralData.DataSource == null || ((DataTable)dgvMineralData.DataSource).Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 创建保存文件对话框
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"脆性指数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 调用导出方法
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"开始生成曲线，原始数据表行数: {mineralData?.Rows.Count ?? 0}");

                // 清除现有数据
                chartBrittleness.Series.Clear();
                dataPoints.Clear();  // 清空数据点列表

                // 创建曲线系列 - 使用Line类型实现直线连接
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Line,
                    Color = Color.Cyan,
                    BorderWidth = 2
                };

                // 创建点系列 - 用于交互但不可见
                var pointSeries = new Series("矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Transparent,  // 透明色使点不可见
                    MarkerSize = 8,
                    MarkerStyle = MarkerStyle.Circle,
                    IsVisibleInLegend = false   // 在图例中不显示
                };

                // 始终使用当前数据表中的数据
                DataTable currentData = null;
                if (dgvMineralData.DataSource is DataTable dataTable && dataTable.Rows.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"从当前数据表中获取数据，行数: {dataTable.Rows.Count}");
                    currentData = dataTable;
                }
                else
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保使用当前数据表而不是mineralData
                mineralData = currentData.Copy(); // 创建数据表的副本，避免引用问题

                List<double> topDepths = new List<double>();

                // 收集数据点
                for (int i = 0; i < mineralData.Rows.Count; i++)
                {
                    var row = mineralData.Rows[i];
                    double topDepth = 0.0;
                    double bottomDepth = 0.0;
                    double brittleIndex = 0.0;

                    try
                    {
                        // 尝试使用列名访问
                        if (mineralData.Columns.Contains("顶深/m"))
                            topDepth = ParseCell(row["顶深/m"]);
                        else if (mineralData.Columns.Contains("顶深"))
                            topDepth = ParseCell(row["顶深"]);
                        else if (columnPositions.ContainsKey("顶深"))
                            topDepth = ParseCell(row[columnPositions["顶深"].col]);

                        if (mineralData.Columns.Contains("底深/m"))
                            bottomDepth = ParseCell(row["底深/m"]);
                        else if (mineralData.Columns.Contains("底深"))
                            bottomDepth = ParseCell(row["底深"]);
                        else if (columnPositions.ContainsKey("底深"))
                            bottomDepth = ParseCell(row[columnPositions["底深"].col]);

                        if (mineralData.Columns.Contains("脆性指数"))
                            brittleIndex = ParseCell(row["脆性指数"]);
                        else if (mineralData.Columns.Contains("脆性"))
                            brittleIndex = ParseCell(row["脆性"]);
                        else if (columnPositions.ContainsKey("脆性"))
                            brittleIndex = ParseCell(row[columnPositions["脆性"].col]);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取数据点失败: {ex.Message}");
                    }

                    // 检查数据是否有效（非NaN、非无穷大、非0）
                    bool isTopDepthValid = !double.IsNaN(topDepth) && !double.IsInfinity(topDepth);
                    bool isBottomDepthValid = !double.IsNaN(bottomDepth) && !double.IsInfinity(bottomDepth);
                    bool isBrittleValid = !double.IsNaN(brittleIndex) && !double.IsInfinity(brittleIndex) && brittleIndex != 0;

                    if (isTopDepthValid && isBottomDepthValid && isBrittleValid)
                    {
                        // 确保脆性指数值已乘以100
                        if (brittleIndex < 1.0)
                        {
                            brittleIndex *= 100;
                        }

                        // 保留更高精度的脆性指数值，但保持深度的两位小数精度
                        var newPoint = new DataPoint
                        {
                            TopDepth = Math.Round(topDepth, 2),
                            BottomDepth = Math.Round(bottomDepth, 2),
                            BrittleIndex = Math.Round(brittleIndex, 4), // 保留四位小数精度
                            RowIndex = i
                        };

                        // 生成唯一的geoID
                        newPoint.GenerateGeoID();
                        System.Diagnostics.Debug.WriteLine($"为数据点生成GeoID: {newPoint.GeoID}");

                        dataPoints.Add(newPoint);

                        // 输出精确的数据点信息，包括原始值和四舍五入后的值
                        System.Diagnostics.Debug.WriteLine($"数据点精度处理: 原始脆性指数={brittleIndex:F6}, 处理后={Math.Round(brittleIndex, 2):F2}");
                        topDepths.Add(topDepth);
                        System.Diagnostics.Debug.WriteLine($"添加数据点: 顶深={topDepth}, 底深={bottomDepth}, 脆性指数={brittleIndex}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过无效数据点: 顶深={topDepth}(有效:{isTopDepthValid}), 底深={bottomDepth}(有效:{isBottomDepthValid}), 脆性指数={brittleIndex}(有效:{isBrittleValid})");
                    }
                }

                // 检查是否有足够的数据点
                if (topDepths.Count <= 0)
                {
                    MessageBox.Show("没有有效的数据点可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保有足够的数据范围
                double minTopDepth = topDepths.Min();
                double maxTopDepth = topDepths.Max();

                // 如果最小值和最大值相同，添加一个小的偏移量以避免坐标轴错误
                if (Math.Abs(maxTopDepth - minTopDepth) < 0.001)
                {
                    maxTopDepth += 1.0;
                }

                var chartArea = chartBrittleness.ChartAreas[0];
                chartArea.AxisY.CustomLabels.Clear();

                // 更新Y轴刻度数组
                yAxisLabels.Clear();
                yAxisLabels.AddRange(topDepths.OrderBy(d => d));

                // 设置Y轴范围
                chartArea.AxisY.Minimum = minTopDepth;
                chartArea.AxisY.Maximum = maxTopDepth;

                // 设置X轴范围（脆性指数范围）
                // 计算实际的脆性指数范围
                double minBrittle = dataPoints.Count > 0 ? dataPoints.Min(p => p.BrittleIndex) : 0;
                double maxBrittle = dataPoints.Count > 0 ? dataPoints.Max(p => p.BrittleIndex) : 100;

                // 添加一些边距
                minBrittle = Math.Max(0, minBrittle - 5);
                maxBrittle = maxBrittle + 5;

                chartArea.AxisX.Minimum = minBrittle;
                chartArea.AxisX.Maximum = maxBrittle;

                // 设置X轴的初始视图范围 - 只显示部分数据
                double xVisibleRange = (maxBrittle - minBrittle) * 0.6; // 显示60%的X轴数据范围
                double xMidPoint = (minBrittle + maxBrittle) / 2;
                double xViewMin = xMidPoint - xVisibleRange / 2;
                double xViewMax = xMidPoint + xVisibleRange / 2;

                // 确保X轴视图范围在数据范围内
                xViewMin = Math.Max(xViewMin, minBrittle);
                xViewMax = Math.Min(xViewMax, maxBrittle);

                // 设置X轴初始视图范围
                chartArea.AxisX.ScaleView.Zoom(xViewMin, xViewMax);

                // 更新X轴缩放比例
                currentXZoom = (maxBrittle - minBrittle) / (xViewMax - xViewMin);

                // 计算初始缩放比例
                double dataHeight = maxTopDepth - minTopDepth;
                double chartHeight = chartBrittleness.Height;
                double initialZoom = dataHeight / chartHeight;

                // 重置缩放比例
                currentZoom = 1.0;
                currentXZoom = 1.0;

                // 重置缩放视图
                chartArea.AxisY.ScaleView.ZoomReset();
                chartArea.AxisX.ScaleView.ZoomReset();

                // 启用缩放功能
                chartArea.AxisY.ScaleView.Zoomable = true;
                chartArea.AxisX.ScaleView.Zoomable = true;

                // 设置滚动条可见并始终显示
                chartArea.AxisY.ScrollBar.Enabled = true;
                chartArea.AxisX.ScrollBar.Enabled = true;
                chartArea.AxisY.ScrollBar.IsPositionedInside = false; // 将滚动条放在外部
                chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                chartArea.AxisY.ScrollBar.Size = 15; // 增加滚动条大小
                chartArea.AxisX.ScrollBar.Size = 15;

                // 设置滚动条颜色
                chartArea.AxisY.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisX.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisY.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);
                chartArea.AxisX.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);

                // 设置滚动条样式
                chartArea.AxisY.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;
                chartArea.AxisX.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;

                // 设置滚动条行为
                chartArea.AxisY.ScrollBar.IsPositionedInside = false;
                chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                chartArea.CursorY.IsUserEnabled = true;
                chartArea.CursorY.IsUserSelectionEnabled = true;
                chartArea.CursorX.IsUserEnabled = true;
                chartArea.CursorX.IsUserSelectionEnabled = true;

                // 设置滚动条属性
                // 注意：SmallScrollSize 和 SmallScrollMinSize 在当前版本的库中不可用

                // 设置初始缩放范围 - 不再尝试将所有点集中在面板中
                // 计算合适的初始视图范围
                double visibleRange = (maxTopDepth - minTopDepth) * 0.3; // 只显示30%的数据范围
                double midPoint = (minTopDepth + maxTopDepth) / 2;
                double viewMin = midPoint - visibleRange / 2;
                double viewMax = midPoint + visibleRange / 2;

                // 确保视图范围在数据范围内
                viewMin = Math.Max(viewMin, minTopDepth);
                viewMax = Math.Min(viewMax, maxTopDepth);

                // 设置初始视图范围
                chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);

                // 更新缩放比例
                currentZoom = (maxTopDepth - minTopDepth) / (viewMax - viewMin);

                // 设置Y轴属性
                chartArea.AxisY.LabelStyle.Format = "0.00";
                chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

                // 显示初始Y轴刻度
                UpdateYAxisLabels();

                // 添加数据点 - 按行索引排序后添加到图表
                System.Diagnostics.Debug.WriteLine($"按行索引排序后添加数据点到图表:");

                // 先按行索引排序数据点
                var orderedPoints = dataPoints.OrderBy(p => p.RowIndex).ToList();

                // 对数据点进行抽样，减少大数据量时的点数
                var sampledPoints = SampleDataPoints(orderedPoints);
                System.Diagnostics.Debug.WriteLine($"原始数据点数量: {orderedPoints.Count}, 抽样后数据点数量: {sampledPoints.Count}");

                // 输出抽样后的数据点信息
                for (int i = 0; i < sampledPoints.Count; i++)
                {
                    var point = sampledPoints[i];
                    System.Diagnostics.Debug.WriteLine($"  数据点[{i}]: 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");

                    // 添加到曲线图表
                    series.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 为了保持交互性，仍然添加所有点到点系列，但设置为透明
                pointSeries.Color = Color.Transparent;

                // 创建可见的点系列，只显示抽样后的点
                var visiblePointSeries = new Series("可见矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerSize = 8,
                    MarkerStyle = MarkerStyle.Circle
                };

                // 添加抽样后的点到可见点系列
                foreach (var point in sampledPoints)
                {
                    visiblePointSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 添加所有原始点到不可见点系列（用于交互）
                foreach (var point in orderedPoints)
                {
                    pointSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 输出图表点信息
                System.Diagnostics.Debug.WriteLine($"图表点信息:");
                for (int i = 0; i < pointSeries.Points.Count; i++)
                {
                    var chartPoint = pointSeries.Points[i];
                    System.Diagnostics.Debug.WriteLine($"  图表点[{i}]: 脆性指数={chartPoint.XValue:F6}, 深度={chartPoint.YValues[0]:F6}");
                }

                // 添加系列到图表
                chartBrittleness.Series.Add(series);
                chartBrittleness.Series.Add(pointSeries);
                chartBrittleness.Series.Add(visiblePointSeries);
                selectedRows.Clear();  // 重置选中行

                // 强制刷新图表
                chartBrittleness.Invalidate();

                // 不再调用SynchronizeGeoIDs，避免循环调用

                MessageBox.Show($"成功生成曲线，数据点数量: {dataPoints.Count}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDeletePoint_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有选中的行
                if (selectedRows.Count == 0)
                {
                    MessageBox.Show("请先选择要删除的数据点", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确认删除
                if (MessageBox.Show($"确定要删除选中的数据点吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // 获取数据表
                    var dataTable = (DataTable)dgvMineralData.DataSource;

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"删除前数据表行数: {dataTable.Rows.Count}");
                    System.Diagnostics.Debug.WriteLine($"删除前原始数据表行数: {mineralData.Rows.Count}");
                    System.Diagnostics.Debug.WriteLine($"删除前数据点数量: {dataPoints.Count}");
                    System.Diagnostics.Debug.WriteLine($"要删除的行: {string.Join(", ", selectedRows)}");

                    // 将选中的行转换为数组并按降序排序，以避免删除时索引变化引起的问题
                    var rowsToDelete = selectedRows.ToArray();
                    Array.Sort(rowsToDelete, (a, b) => b.CompareTo(a)); // 降序排序

                    // 在删除前记录最大行索引
                    int maxRowIndex = rowsToDelete.Length > 0 ? rowsToDelete.Min() : -1;

                    // 收集要删除的数据点的GeoID
                    List<string> geoIdsToDelete = new List<string>();
                    foreach (int rowIndex in rowsToDelete)
                    {
                        var pointsToDelete = dataPoints.Where(p => p.RowIndex == rowIndex).ToList();
                        foreach (var point in pointsToDelete)
                        {
                            if (!string.IsNullOrEmpty(point.GeoID))
                            {
                                geoIdsToDelete.Add(point.GeoID);
                                System.Diagnostics.Debug.WriteLine($"标记要删除的数据点: GeoID={point.GeoID}, 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F2}");
                            }
                            else
                            {
                                // 如果没有GeoID，则使用ID
                                System.Diagnostics.Debug.WriteLine($"数据点没有GeoID，使用ID: ID={point.Id}, 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F2}");
                            }
                        }
                    }

                    // 删除选中的行
                    foreach (int rowIndex in rowsToDelete)
                    {
                        if (rowIndex < dataTable.Rows.Count)
                        {
                            dataTable.Rows.RemoveAt(rowIndex);
                            System.Diagnostics.Debug.WriteLine($"从数据表中删除行: {rowIndex}");
                        }

                        // 同时从原始数据表中删除相应的行
                        if (rowIndex < mineralData.Rows.Count)
                        {
                            mineralData.Rows.RemoveAt(rowIndex);
                            System.Diagnostics.Debug.WriteLine($"从原始数据表中删除行: {rowIndex}");
                        }
                    }

                    // 使用GeoID删除数据点，而不是使用行索引
                    int removedPoints = 0;
                    if (geoIdsToDelete.Count > 0)
                    {
                        removedPoints = dataPoints.RemoveAll(p => !string.IsNullOrEmpty(p.GeoID) && geoIdsToDelete.Contains(p.GeoID));
                        System.Diagnostics.Debug.WriteLine($"使用GeoID删除了 {removedPoints} 个数据点");
                    }
                    else
                    {
                        // 如果没有GeoID，则使用行索引删除
                        removedPoints = dataPoints.RemoveAll(p => rowsToDelete.Contains(p.RowIndex));
                        System.Diagnostics.Debug.WriteLine($"使用行索引删除了 {removedPoints} 个数据点");
                    }

                    // 调整后续行的RowIndex - 使用更精确的方法
                    System.Diagnostics.Debug.WriteLine($"开始调整数据点行索引...");

                    // 输出删除前的数据点信息
                    System.Diagnostics.Debug.WriteLine($"删除前的数据点信息:");
                    foreach (var point in dataPoints)
                    {
                        System.Diagnostics.Debug.WriteLine($"  数据点: ID={point.Id}, 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");
                    }

                    // 重新计算每个数据点的行索引
                    foreach (var point in dataPoints)
                    {
                        // 计算在该点之前删除的行数
                        int deletedBefore = rowsToDelete.Count(idx => idx < point.RowIndex);
                        if (deletedBefore > 0)
                        {
                            int oldIndex = point.RowIndex;
                            point.RowIndex -= deletedBefore;
                            System.Diagnostics.Debug.WriteLine($"  调整数据点行索引: {oldIndex} -> {point.RowIndex}, 减少了 {deletedBefore}");
                        }
                    }

                    // 输出删除后的数据点信息
                    System.Diagnostics.Debug.WriteLine($"删除后的数据点信息:");
                    foreach (var point in dataPoints)
                    {
                        System.Diagnostics.Debug.WriteLine($"  数据点: ID={point.Id}, 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");
                    }

                    // 验证行索引是否与数据表一致
                    System.Diagnostics.Debug.WriteLine($"验证行索引与数据表一致性:");
                    for (int i = 0; i < Math.Min(dataTable.Rows.Count, 10); i++)
                    {
                        var row = dataTable.Rows[i];
                        var matchingPoints = dataPoints.Where(p => p.RowIndex == i).ToList();

                        if (matchingPoints.Count > 0)
                        {
                            foreach (var point in matchingPoints)
                            {
                                System.Diagnostics.Debug.WriteLine($"  行 {i}: 数据点 ID={point.Id}, 脆性指数={point.BrittleIndex:F6}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"  行 {i}: 没有匹配的数据点");
                        }
                    }

                    // 输出删除行后的所有数据点索引
                    System.Diagnostics.Debug.WriteLine($"删除行后 dataPoints 索引:");
                    foreach (var dp in dataPoints)
                    {
                        System.Diagnostics.Debug.WriteLine($"RowIndex={dp.RowIndex}, Brittle={dp.BrittleIndex:F2}");
                    }

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"删除后数据表行数: {dataTable.Rows.Count}");
                    System.Diagnostics.Debug.WriteLine($"删除后原始数据表行数: {mineralData.Rows.Count}");

                    // 清除选中行列表
                    selectedRows.Clear();

                    // 强制刷新数据表
                    dgvMineralData.Refresh();
                    dgvMineralData.Update();

                    // 强制刷新数据表的数据源
                    dgvMineralData.DataSource = null;
                    dgvMineralData.DataSource = dataTable;

                    // 确保数据表变化生效
                    dataTable.AcceptChanges();

                    // 完全清除图表
                    chartBrittleness.Series.Clear();

                    // 重置图表区域
                    if (chartBrittleness.ChartAreas.Count > 0)
                    {
                        var chartArea = chartBrittleness.ChartAreas[0];
                        chartArea.AxisX.ScaleView.ZoomReset();
                        chartArea.AxisY.ScaleView.ZoomReset();
                        chartArea.AxisX.CustomLabels.Clear();
                        chartArea.AxisY.CustomLabels.Clear();
                    }

                    // 完全重新加载数据表和图表
                    CompletelyReloadChartAndData();

                    MessageBox.Show("数据点删除成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除数据点时出错: {ex.Message}");
                MessageBox.Show($"删除数据点时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查图表是否有数据
                if (chartBrittleness.Series.Count == 0 || dataPoints.Count == 0)
                {
                    MessageBox.Show("图表没有数据需要重置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 重置缩放比例
                currentZoom = 1.0;
                currentXZoom = 1.0;

                // 获取图表区域
                var chartArea = chartBrittleness.ChartAreas[0];

                // 计算数据范围
                double minTopDepth = dataPoints.Min(p => p.TopDepth);
                double maxTopDepth = dataPoints.Max(p => p.TopDepth);
                double minBrittle = dataPoints.Min(p => p.BrittleIndex);
                double maxBrittle = dataPoints.Max(p => p.BrittleIndex);

                // 添加边距
                minBrittle = Math.Max(0, minBrittle - 5);
                maxBrittle = maxBrittle + 5;

                // 如果最小值和最大值相同，添加一个小的偏移量
                if (Math.Abs(maxTopDepth - minTopDepth) < 0.001)
                {
                    maxTopDepth += 1.0;
                }

                // 设置轴范围
                chartArea.AxisY.Minimum = minTopDepth;
                chartArea.AxisY.Maximum = maxTopDepth;
                chartArea.AxisX.Minimum = minBrittle;
                chartArea.AxisX.Maximum = maxBrittle;

                // 重置为初始视图状态，而不是完全重置
                // 计算Y轴合适的初始视图范围
                double visibleRange = (maxTopDepth - minTopDepth) * 0.3; // 只显示30%的数据范围
                double midPoint = (minTopDepth + maxTopDepth) / 2;
                double viewMin = midPoint - visibleRange / 2;
                double viewMax = midPoint + visibleRange / 2;

                // 确保视图范围在数据范围内
                viewMin = Math.Max(viewMin, minTopDepth);
                viewMax = Math.Min(viewMax, maxTopDepth);

                // 计算X轴合适的初始视图范围
                double xVisibleRange = (maxBrittle - minBrittle) * 0.6; // 显示60%的X轴数据范围
                double xMidPoint = (minBrittle + maxBrittle) / 2;
                double xViewMin = xMidPoint - xVisibleRange / 2;
                double xViewMax = xMidPoint + xVisibleRange / 2;

                // 确保X轴视图范围在数据范围内
                xViewMin = Math.Max(xViewMin, minBrittle);
                xViewMax = Math.Min(xViewMax, maxBrittle);

                // 设置初始视图范围
                chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);
                chartArea.AxisX.ScaleView.Zoom(xViewMin, xViewMax);

                // 更新缩放比例
                currentZoom = (maxTopDepth - minTopDepth) / (viewMax - viewMin);
                currentXZoom = (maxBrittle - minBrittle) / (xViewMax - xViewMin);

                // 更新Y轴刻度
                UpdateYAxisLabels();

                // 强制刷新图表
                chartBrittleness.Invalidate();

                MessageBox.Show("视图已重置到初始状态", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置视图时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查图表是否有数据
                if (chartBrittleness.Series.Count == 0 || dataPoints.Count == 0)
                {
                    MessageBox.Show("图表没有数据可以保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 创建保存文件对话框
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "图像文件 (*.png)|*.png|所有文件 (*.*)|*.*";
                sfd.Title = "保存图表图像";
                sfd.FileName = $"脆性指数曲线_{DateTime.Now:yyyyMMdd_HHmmss}.png";

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // 保存当前缩放状态
                        var chartArea = chartBrittleness.ChartAreas[0];
                        double currentMin = chartArea.AxisY.ScaleView.ViewMinimum;
                        double currentMax = chartArea.AxisY.ScaleView.ViewMaximum;
                        double currentZoomLevel = currentZoom;

                        // 暂时重置缩放以保存完整图表
                        bool isZoomed = chartArea.AxisY.ScaleView.IsZoomed;
                        if (isZoomed)
                        {
                            chartArea.AxisY.ScaleView.ZoomReset();
                        }

                        // 保存图表为图像
                        chartBrittleness.SaveImage(sfd.FileName, ChartImageFormat.Png);

                        // 恢复缩放状态
                        if (isZoomed)
                        {
                            chartArea.AxisY.ScaleView.Zoom(currentMin, currentMax);
                            currentZoom = currentZoomLevel;
                            UpdateYAxisLabels();
                        }

                        MessageBox.Show($"图像已保存到 {sfd.FileName}", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            try
            {
                if (MessageBox.Show("确定要退出登录吗？未保存的数据将会丢失。", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // 在实际应用中，这里应该返回到登录窗体
                    // 例如： this.Hide(); new LoginForm().Show();

                    // 模拟退出登录
                    MessageBox.Show("即将退出登录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 关闭当前窗体
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"退出登录时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 新方法：处理脆性指数为100的点
        private void HandleBrittlenessIndex100Point()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始处理脆性指数为100的点...");

                // 查找脆性指数为100的数据点
                var point100 = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - 100) < 0.01);
                if (point100 == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到脆性指数为100的数据点");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"找到脆性指数为100的数据点: GeoID={point100.GeoID}, 深度={point100.TopDepth:F2}");

                // 在数据表中查找脆性指数为100的行
                int rowIndex = -1;
                for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                {
                    try
                    {
                        foreach (DataGridViewColumn col in dgvMineralData.Columns)
                        {
                            if (col.HeaderText.Trim().ToLower().Contains("脆性") && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                            {
                                string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                if (double.TryParse(cellValue, out double rowBI) && Math.Abs(rowBI - 100) < 0.01)
                                {
                                    rowIndex = i;
                                    System.Diagnostics.Debug.WriteLine($"在数据表中找到脆性指数为100的行: {i}");
                                    break;
                                }
                            }
                        }

                        if (rowIndex >= 0)
                            break;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"检查行 {i} 时出错: {ex.Message}");
                    }
                }

                if (rowIndex >= 0)
                {
                    // 将GeoID存储到该行的Tag中
                    dgvMineralData.Rows[rowIndex].Tag = point100.GeoID;
                    System.Diagnostics.Debug.WriteLine($"将GeoID存储到行 {rowIndex} 的Tag中: {point100.GeoID}");

                    // 选中该行
                    dgvMineralData.ClearSelection();
                    dgvMineralData.Rows[rowIndex].Selected = true;
                    if (dgvMineralData.Rows[rowIndex].Cells.Count > 0)
                    {
                        dgvMineralData.CurrentCell = dgvMineralData.Rows[rowIndex].Cells[0];
                        dgvMineralData.FirstDisplayedScrollingRowIndex = rowIndex;
                    }

                    // 更新选中行集合
                    selectedRows.Clear();
                    selectedRows.Add(rowIndex);

                    // 更新高亮显示
                    UpdateHighlights();

                    // 创建新的高亮点Series
                    string seriesName = $"HighlightPoints_{DateTime.Now.Ticks}";
                    Series highlightSeries = new Series(seriesName)
                    {
                        ChartType = SeriesChartType.Point,
                        Color = Color.Gold,
                        MarkerSize = 10,
                        MarkerStyle = MarkerStyle.Circle,
                        LegendText = "选中点"
                    };

                    // 添加选中的数据点
                    highlightSeries.Points.AddXY(point100.BrittleIndex, point100.TopDepth);

                    // 删除现有的高亮系列
                    var seriesToRemove = chartBrittleness.Series.Where(s => s.Name.StartsWith("Highlight") || s.Name.Contains("选中点")).ToList();
                    foreach (var series in seriesToRemove)
                    {
                        chartBrittleness.Series.Remove(series);
                    }

                    // 添加新的高亮系列
                    chartBrittleness.Series.Add(highlightSeries);

                    // 更新标题
                    lblChartTitle.Text = $"选中点: 脆性指数={point100.BrittleIndex:F2}%, 深度={point100.TopDepth:F2}m";

                    // 使用BeginInvoke异步更新UI，避免可能的递归调用
                    if (!this.IsDisposed && this.IsHandleCreated)
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            if (chartBrittleness != null && !chartBrittleness.IsDisposed)
                            {
                                chartBrittleness.Update();
                            }
                        }));
                    }
                    chartBrittleness.Update();

                    System.Diagnostics.Debug.WriteLine("处理脆性指数为100的点完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("在数据表中未找到脆性指数为100的行");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理脆性指数为100的点时出错: {ex.Message}");
            }
        }

        // 新方法：重新生成图表，但不调用BtnGenerateCurve_Click方法
        private void RegenerateChartWithoutCallingButton()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始重新生成图表...");

                // 清除现有数据
                chartBrittleness.Series.Clear();
                dataPoints.Clear();  // 清空数据点列表

                // 创建曲线系列 - 使用Line类型实现直线连接
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Line,
                    Color = Color.Cyan,
                    BorderWidth = 2
                };

                // 创建点系列 - 用于交互但不可见
                var pointSeries = new Series("矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Transparent,  // 透明色使点不可见
                    MarkerSize = 8,
                    MarkerStyle = MarkerStyle.Circle,
                    IsVisibleInLegend = false   // 在图例中不显示
                };

                // 始终使用当前数据表中的数据
                DataTable currentData = null;
                if (dgvMineralData.DataSource is DataTable dataTable && dataTable.Rows.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"从当前数据表中获取数据，行数: {dataTable.Rows.Count}");
                    currentData = dataTable.Copy(); // 创建数据表的副本
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("没有数据可以生成曲线！");
                    return;
                }

                // 确保使用当前数据表而不是mineralData
                mineralData = currentData.Copy(); // 创建数据表的副本，避免引用问题

                List<double> topDepths = new List<double>();

                // 收集数据点
                for (int i = 0; i < mineralData.Rows.Count; i++)
                {
                    var row = mineralData.Rows[i];
                    double topDepth = 0.0;
                    double bottomDepth = 0.0;
                    double brittleIndex = 0.0;

                    try
                    {
                        // 尝试使用列名访问
                        if (mineralData.Columns.Contains("顶深/m"))
                            topDepth = ParseCell(row["顶深/m"]);
                        else if (mineralData.Columns.Contains("顶深"))
                            topDepth = ParseCell(row["顶深"]);
                        else if (columnPositions.ContainsKey("顶深"))
                            topDepth = ParseCell(row[columnPositions["顶深"].col]);

                        if (mineralData.Columns.Contains("底深/m"))
                            bottomDepth = ParseCell(row["底深/m"]);
                        else if (mineralData.Columns.Contains("底深"))
                            bottomDepth = ParseCell(row["底深"]);
                        else if (columnPositions.ContainsKey("底深"))
                            bottomDepth = ParseCell(row[columnPositions["底深"].col]);

                        if (mineralData.Columns.Contains("脆性指数"))
                            brittleIndex = ParseCell(row["脆性指数"]);
                        else if (mineralData.Columns.Contains("脆性"))
                            brittleIndex = ParseCell(row["脆性"]);
                        else if (columnPositions.ContainsKey("脆性"))
                            brittleIndex = ParseCell(row[columnPositions["脆性"].col]);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取数据点失败: {ex.Message}");
                    }

                    // 检查数据是否有效（非NaN、非无穷大、非0）
                    bool isTopDepthValid = !double.IsNaN(topDepth) && !double.IsInfinity(topDepth);
                    bool isBottomDepthValid = !double.IsNaN(bottomDepth) && !double.IsInfinity(bottomDepth);
                    bool isBrittleValid = !double.IsNaN(brittleIndex) && !double.IsInfinity(brittleIndex) && brittleIndex != 0;

                    if (isTopDepthValid && isBottomDepthValid && isBrittleValid)
                    {
                        // 确保脆性指数值已乘以100
                        if (brittleIndex < 1.0)
                        {
                            brittleIndex *= 100;
                        }

                        // 保留更高精度的脆性指数值，但保持深度的两位小数精度
                        var newPoint = new DataPoint
                        {
                            TopDepth = Math.Round(topDepth, 2),
                            BottomDepth = Math.Round(bottomDepth, 2),
                            BrittleIndex = Math.Round(brittleIndex, 4), // 保留四位小数精度
                            RowIndex = i
                        };

                        // 生成唯一的geoID
                        newPoint.GenerateGeoID();
                        System.Diagnostics.Debug.WriteLine($"为数据点生成GeoID: {newPoint.GeoID}");

                        dataPoints.Add(newPoint);

                        // 输出精确的数据点信息，包括原始值和四舍五入后的值
                        System.Diagnostics.Debug.WriteLine($"数据点精度处理: 原始脆性指数={brittleIndex:F6}, 处理后={Math.Round(brittleIndex, 2):F2}");
                        topDepths.Add(topDepth);
                        System.Diagnostics.Debug.WriteLine($"添加数据点: 顶深={topDepth}, 底深={bottomDepth}, 脆性指数={brittleIndex}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过无效数据点: 顶深={topDepth}(有效:{isTopDepthValid}), 底深={bottomDepth}(有效:{isBottomDepthValid}), 脆性指数={brittleIndex}(有效:{isBrittleValid})");
                    }
                }

                // 检查是否有足够的数据点
                if (topDepths.Count <= 0)
                {
                    System.Diagnostics.Debug.WriteLine("没有有效的数据点可以生成曲线！");
                    return;
                }

                // 确保有足够的数据范围
                double minTopDepth = topDepths.Min();
                double maxTopDepth = topDepths.Max();

                // 如果最小值和最大值相同，添加一个小的偏移量以避免坐标轴错误
                if (Math.Abs(maxTopDepth - minTopDepth) < 0.001)
                {
                    maxTopDepth += 1.0;
                }

                var chartArea = chartBrittleness.ChartAreas[0];
                chartArea.AxisY.CustomLabels.Clear();

                // 更新Y轴刻度数组
                yAxisLabels.Clear();
                yAxisLabels.AddRange(topDepths.OrderBy(d => d));

                // 设置Y轴范围
                chartArea.AxisY.Minimum = minTopDepth;
                chartArea.AxisY.Maximum = maxTopDepth;

                // 设置X轴范围（脆性指数范围）
                // 计算实际的脆性指数范围
                double minBrittle = dataPoints.Count > 0 ? dataPoints.Min(p => p.BrittleIndex) : 0;
                double maxBrittle = dataPoints.Count > 0 ? dataPoints.Max(p => p.BrittleIndex) : 100;

                // 添加一些边距
                minBrittle = Math.Max(0, minBrittle - 5);
                maxBrittle = maxBrittle + 5;

                chartArea.AxisX.Minimum = minBrittle;
                chartArea.AxisX.Maximum = maxBrittle;

                // 设置X轴的初始视图范围 - 只显示部分数据
                double xVisibleRange = (maxBrittle - minBrittle) * 0.6; // 显示60%的X轴数据范围
                double xMidPoint = (minBrittle + maxBrittle) / 2;
                double xViewMin = xMidPoint - xVisibleRange / 2;
                double xViewMax = xMidPoint + xVisibleRange / 2;

                // 确保X轴视图范围在数据范围内
                xViewMin = Math.Max(xViewMin, minBrittle);
                xViewMax = Math.Min(xViewMax, maxBrittle);

                // 设置X轴初始视图范围
                chartArea.AxisX.ScaleView.Zoom(xViewMin, xViewMax);

                // 更新X轴缩放比例
                currentXZoom = (maxBrittle - minBrittle) / (xViewMax - xViewMin);

                // 计算初始缩放比例
                double dataHeight = maxTopDepth - minTopDepth;
                double chartHeight = chartBrittleness.Height;
                double initialZoom = dataHeight / chartHeight;

                // 重置缩放比例
                currentZoom = 1.0;
                currentXZoom = 1.0;

                // 重置缩放视图
                chartArea.AxisY.ScaleView.ZoomReset();
                chartArea.AxisX.ScaleView.ZoomReset();

                // 启用缩放功能
                chartArea.AxisY.ScaleView.Zoomable = true;
                chartArea.AxisX.ScaleView.Zoomable = true;

                // 设置滚动条可见并始终显示
                chartArea.AxisY.ScrollBar.Enabled = true;
                chartArea.AxisX.ScrollBar.Enabled = true;
                chartArea.AxisY.ScrollBar.IsPositionedInside = false; // 将滚动条放在外部
                chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                chartArea.AxisY.ScrollBar.Size = 15; // 增加滚动条大小
                chartArea.AxisX.ScrollBar.Size = 15;

                // 设置滚动条颜色
                chartArea.AxisY.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisX.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisY.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);
                chartArea.AxisX.ScrollBar.ButtonColor = Color.FromArgb(80, 80, 80);

                // 设置滚动条样式
                chartArea.AxisY.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;
                chartArea.AxisX.ScrollBar.ButtonStyle = ScrollBarButtonStyles.All;

                // 设置滚动条行为
                chartArea.AxisY.ScrollBar.IsPositionedInside = false;
                chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                chartArea.CursorY.IsUserEnabled = true;
                chartArea.CursorY.IsUserSelectionEnabled = true;
                chartArea.CursorX.IsUserEnabled = true;
                chartArea.CursorX.IsUserSelectionEnabled = true;

                // 设置滚动条属性
                // 注意：SmallScrollSize 和 SmallScrollMinSize 在当前版本的库中不可用

                // 设置初始缩放范围 - 不再尝试将所有点集中在面板中
                // 计算合适的初始视图范围
                double visibleRange = (maxTopDepth - minTopDepth) * 0.3; // 只显示30%的数据范围
                double midPoint = (minTopDepth + maxTopDepth) / 2;
                double viewMin = midPoint - visibleRange / 2;
                double viewMax = midPoint + visibleRange / 2;

                // 确保视图范围在数据范围内
                viewMin = Math.Max(viewMin, minTopDepth);
                viewMax = Math.Min(viewMax, maxTopDepth);

                // 设置初始视图范围
                chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);

                // 更新缩放比例
                currentZoom = (maxTopDepth - minTopDepth) / (viewMax - viewMin);

                // 设置Y轴属性
                chartArea.AxisY.LabelStyle.Format = "0.00";
                chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

                // 显示初始Y轴刻度
                UpdateYAxisLabels();

                // 添加数据点 - 按行索引排序后添加到图表
                System.Diagnostics.Debug.WriteLine($"按行索引排序后添加数据点到图表:");

                // 先按行索引排序数据点
                var orderedPoints = dataPoints.OrderBy(p => p.RowIndex).ToList();

                // 对数据点进行抽样，减少大数据量时的点数
                var sampledPoints = SampleDataPoints(orderedPoints);
                System.Diagnostics.Debug.WriteLine($"原始数据点数量: {orderedPoints.Count}, 抽样后数据点数量: {sampledPoints.Count}");

                // 输出抽样后的数据点信息
                for (int i = 0; i < sampledPoints.Count; i++)
                {
                    var point = sampledPoints[i];
                    System.Diagnostics.Debug.WriteLine($"  数据点[{i}]: 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");

                    // 添加到曲线图表
                    series.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 为了保持交互性，仍然添加所有点到点系列，但设置为透明
                pointSeries.Color = Color.Transparent;

                // 创建可见的点系列，只显示抽样后的点
                var visiblePointSeries = new Series("可见矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerSize = 8,
                    MarkerStyle = MarkerStyle.Circle
                };

                // 添加抽样后的点到可见点系列
                foreach (var point in sampledPoints)
                {
                    visiblePointSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 添加所有原始点到不可见点系列（用于交互）
                foreach (var point in orderedPoints)
                {
                    pointSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 输出图表点信息
                System.Diagnostics.Debug.WriteLine($"图表点信息:");
                for (int i = 0; i < pointSeries.Points.Count; i++)
                {
                    var chartPoint = pointSeries.Points[i];
                    System.Diagnostics.Debug.WriteLine($"  图表点[{i}]: 脆性指数={chartPoint.XValue:F6}, 深度={chartPoint.YValues[0]:F6}");
                }

                // 添加系列到图表
                chartBrittleness.Series.Add(series);
                chartBrittleness.Series.Add(pointSeries);
                chartBrittleness.Series.Add(visiblePointSeries);
                selectedRows.Clear();  // 重置选中行

                // 强制重绘图表
                chartBrittleness.Invalidate();

                System.Diagnostics.Debug.WriteLine($"成功生成曲线，数据点数量: {dataPoints.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重新生成图表时出错: {ex.Message}");
            }
        }

        // 新方法：完全重新加载数据表和图表
        private void CompletelyReloadChartAndData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始完全重新加载数据表和图表...");

                // 获取当前数据表
                DataTable currentData = null;
                if (dgvMineralData.DataSource is DataTable dataTable && dataTable.Rows.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"从当前数据表中获取数据，行数: {dataTable.Rows.Count}");
                    currentData = dataTable.Copy(); // 创建数据表的副本
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("当前数据表为空或无效");
                    return;
                }

                // 完全清除数据点集合
                dataPoints.Clear();
                System.Diagnostics.Debug.WriteLine($"清除后数据点数量: {dataPoints.Count}");

                // 完全清除图表
                chartBrittleness.Series.Clear();

                // 重置图表区域
                if (chartBrittleness.ChartAreas.Count > 0)
                {
                    var chartArea = chartBrittleness.ChartAreas[0];
                    chartArea.AxisX.ScaleView.ZoomReset();
                    chartArea.AxisY.ScaleView.ZoomReset();
                    chartArea.AxisX.CustomLabels.Clear();
                    chartArea.AxisY.CustomLabels.Clear();
                }

                // 重置矿物数据表
                mineralData = null;

                // 重置选中行
                selectedRows.Clear();

                // 重置最后匹配的GeoID
                lastMatchedGeoID = null;

                // 重置悬停点索引
                hoveredPointIndex = null;

                // 强制刷新数据表
                dgvMineralData.Refresh();
                dgvMineralData.Update();

                // 重新设置数据源
                dgvMineralData.DataSource = null;
                dgvMineralData.DataSource = currentData;
                currentData.AcceptChanges();

                // 使用新方法重新生成曲线，避免调用BtnGenerateCurve_Click
                RegenerateChartWithoutCallingButton();

                // 强制重绘图表
                chartBrittleness.Invalidate();
                chartBrittleness.Update();

                System.Diagnostics.Debug.WriteLine("完全重新加载数据表和图表完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"完全重新加载数据表和图表时出错: {ex.Message}");
            }
        }

        // 新方法：同步数据表和图表中的GeoID
        private void SynchronizeGeoIDs()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始同步数据表和图表中的GeoID...");

                // 获取数据表中的GeoID集合
                HashSet<string> dataTableGeoIDs = new HashSet<string>();
                for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                {
                    if (dgvMineralData.Rows[i].Tag != null)
                    {
                        string geoID = dgvMineralData.Rows[i].Tag.ToString();
                        if (!string.IsNullOrEmpty(geoID))
                        {
                            dataTableGeoIDs.Add(geoID);
                        }
                    }
                }

                // 获取图表中的GeoID集合
                HashSet<string> chartGeoIDs = new HashSet<string>();
                foreach (var point in dataPoints)
                {
                    if (!string.IsNullOrEmpty(point.GeoID))
                    {
                        chartGeoIDs.Add(point.GeoID);
                    }
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"数据表中的GeoID数量: {dataTableGeoIDs.Count}");
                System.Diagnostics.Debug.WriteLine($"图表中的GeoID数量: {chartGeoIDs.Count}");
                System.Diagnostics.Debug.WriteLine($"数据表行数: {dgvMineralData.Rows.Count}");
                System.Diagnostics.Debug.WriteLine($"图表数据点数量: {dataPoints.Count}");

                // 如果数据表中的GeoID数量与图表中的不一致，或者数据表行数与图表数据点数量不一致
                if (dataTableGeoIDs.Count != chartGeoIDs.Count || dgvMineralData.Rows.Count != dataPoints.Count)
                {
                    System.Diagnostics.Debug.WriteLine("数据表和图表中的GeoID数量不一致，重新生成曲线...");

                    // 完全清除数据点集合
                    dataPoints.Clear();
                    System.Diagnostics.Debug.WriteLine($"清除后数据点数量: {dataPoints.Count}");

                    // 完全清除图表
                    chartBrittleness.Series.Clear();

                    // 重置图表区域
                    if (chartBrittleness.ChartAreas.Count > 0)
                    {
                        var chartArea = chartBrittleness.ChartAreas[0];
                        chartArea.AxisX.ScaleView.ZoomReset();
                        chartArea.AxisY.ScaleView.ZoomReset();
                        chartArea.AxisX.CustomLabels.Clear();
                        chartArea.AxisY.CustomLabels.Clear();
                    }

                    // 重置矿物数据表
                    mineralData = null;

                    // 重置选中行
                    selectedRows.Clear();

                    // 重置最后匹配的GeoID
                    lastMatchedGeoID = null;

                    // 重置悬停点索引
                    hoveredPointIndex = null;

                    // 强制刷新数据表
                    dgvMineralData.Refresh();
                    dgvMineralData.Update();

                    // 手动重新生成曲线，而不是调用BtnGenerateCurve_Click方法
                    RegenerateChartWithoutCallingButton();

                    // 使用BeginInvoke异步更新UI，避免可能的递归调用
                    if (!this.IsDisposed && this.IsHandleCreated)
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            if (chartBrittleness != null && !chartBrittleness.IsDisposed)
                            {
                                chartBrittleness.Update();
                            }
                        }));
                    }
                    chartBrittleness.Update();

                    System.Diagnostics.Debug.WriteLine("同步完成，曲线已重新生成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("数据表和图表中的GeoID数量一致，无需重新生成曲线");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"同步GeoID时出错: {ex.Message}");
            }
        }

        private void BtnEmergencyExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("确定要紧急退出系统吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void BtnAlgFormulaCal_Click(object sender, EventArgs e)
        {
            try
            {
                // 打开脆性指数计算器窗体
                System.Diagnostics.Debug.WriteLine("打开脆性指数计算器窗体");

                // 使用当前数据表作为源数据
                DataTable sourceData = null;
                if (dgvMineralData.DataSource is DataTable dataTable && dataTable.Rows.Count > 0)
                {
                    sourceData = dataTable.Copy();
                    System.Diagnostics.Debug.WriteLine($"传递数据表到计算器，行数: {sourceData.Rows.Count}");
                }

                // 调用适配器显示脆性指数计算器窗体
                FormAdapter.ShowAlgorithmFormulaCalForm(sourceData);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开脆性指数计算器窗体时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private double ParseCell(object cellValue)
        {
            if (cellValue == null || cellValue == DBNull.Value || string.IsNullOrWhiteSpace(cellValue.ToString()))
            {
                return double.NaN;
            }

            string strValue = cellValue.ToString().Trim();

            // 尝试直接解析为数字
            if (double.TryParse(strValue, out double result))
            {
                if (double.IsInfinity(result) || double.IsNaN(result))
                {
                    return double.NaN;
                }
                return result;
            }

            return double.NaN;
        }

        /// <summary>
        /// 对数据点进行抽样，减少大数据量时的点数
        /// </summary>
        /// <param name="points">原始数据点列表</param>
        /// <returns>抽样后的数据点列表</returns>
        private List<DataPoint> SampleDataPoints(List<DataPoint> points)
        {
            if (points == null || points.Count == 0)
                return new List<DataPoint>();

            // 如果数据点数量少于阈值，直接返回原始数据点
            if (points.Count <= 200)
                return points;

            // 根据数据量动态调整抽样率
            int sampleRate;
            if (points.Count > 1000)
                sampleRate = points.Count / 200; // 对于大数据量，保持约200个点
            else
                sampleRate = 5; // 对于中等数据量，每5个点取1个

            // 确保始终包含第一个和最后一个点
            var result = new List<DataPoint> { points.First() };

            // 使用Douglas-Peucker算法的简化版本，保留关键点
            // 计算每个点的重要性（与相邻点的差异）
            var importanceScores = new Dictionary<int, double>();
            for (int i = 1; i < points.Count - 1; i++)
            {
                // 计算当前点与前后点连线的垂直距离作为重要性指标
                double x1 = points[i - 1].BrittleIndex;
                double y1 = points[i - 1].TopDepth;
                double x2 = points[i + 1].BrittleIndex;
                double y2 = points[i + 1].TopDepth;
                double x0 = points[i].BrittleIndex;
                double y0 = points[i].TopDepth;

                // 计算点到线的距离
                double importance = 0;
                if (Math.Abs(x2 - x1) > 0.001 || Math.Abs(y2 - y1) > 0.001)
                {
                    double a = y2 - y1;
                    double b = x1 - x2;
                    double c = x2 * y1 - x1 * y2;
                    importance = Math.Abs(a * x0 + b * y0 + c) / Math.Sqrt(a * a + b * b);
                }

                importanceScores[i] = importance;
            }

            // 根据重要性排序点
            var sortedIndices = importanceScores.OrderByDescending(kv => kv.Value)
                .Take(points.Count / sampleRate)
                .Select(kv => kv.Key)
                .OrderBy(i => i)
                .ToList();

            // 添加重要点
            foreach (var index in sortedIndices)
            {
                result.Add(points[index]);
            }

            // 确保包含最后一个点
            if (!result.Contains(points.Last()))
            {
                result.Add(points.Last());
            }

            // 按原始顺序排序
            return result.OrderBy(p => p.RowIndex).ToList();
        }

        private void UpdateYAxisLabels()
        {
            try
            {
                if (chartBrittleness == null || chartBrittleness.ChartAreas.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];

                // 获取当前的视图范围
                double viewMin = chartArea.AxisY.ScaleView.ViewMinimum;
                double viewMax = chartArea.AxisY.ScaleView.ViewMaximum;
                double viewSize = viewMax - viewMin;

                // 计算合适的刻度间隔
                double interval = CalculateAxisInterval(viewSize);

                // 清除自定义标签
                chartArea.AxisY.CustomLabels.Clear();

                // 设置刻度间隔
                chartArea.AxisY.LabelStyle.Format = "0.00";
                chartArea.AxisY.IntervalAutoMode = IntervalAutoMode.VariableCount;
                chartArea.AxisY.Interval = interval;
                chartArea.AxisY.MajorGrid.Interval = interval;
                chartArea.AxisY.MajorTickMark.Interval = interval;

                // 更新Y轴标签
                yAxisLabels.Clear();
                double currentLabel = Math.Ceiling(viewMin / interval) * interval;
                while (currentLabel <= viewMax)
                {
                    yAxisLabels.Add(currentLabel);
                    currentLabel += interval;
                }

                // 根据缩放级别调整显示的标签数量
                int labelStep = currentZoom > 3.0 ? 4 : (currentZoom > 2.0 ? 2 : 1);

                for (int i = 0; i < yAxisLabels.Count; i += labelStep)
                {
                    double labelValue = yAxisLabels[i];
                    if (labelValue >= chartArea.AxisY.ScaleView.ViewMinimum &&
                        labelValue <= chartArea.AxisY.ScaleView.ViewMaximum)
                    {
                        var label = chartArea.AxisY.CustomLabels.Add(
                            labelValue - 0.0001,
                            labelValue + 0.0001,
                            $"{labelValue:F2}m");
                        label.RowIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新Y轴刻度时出错: {ex.Message}");
            }
        }

        private double CalculateAxisInterval(double range)
        {
            // 计算合适的刻度间隔，使得刻度数量在合理范围内
            double targetCount = 10; // 目标刻度数量

            // 计算初始间隔
            double initialInterval = range / targetCount;

            // 将间隔调整为整数、十分之一或百分之一
            double exponent = Math.Floor(Math.Log10(initialInterval));
            double factor = Math.Pow(10, exponent);
            double normalizedInterval = initialInterval / factor;

            // 选择最接近的标准间隔（1, 2, 5或它们的十的倍数）
            double standardInterval;
            if (normalizedInterval < 1.5)
                standardInterval = 1;
            else if (normalizedInterval < 3.5)
                standardInterval = 2;
            else if (normalizedInterval < 7.5)
                standardInterval = 5;
            else
                standardInterval = 10;

            return standardInterval * factor;
        }

        private void pnlData_Paint(object sender, PaintEventArgs e)
        {
            // 不需要实现
        }

        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                // 防止重复调用或递归调用
                dgvMineralData.SelectionChanged -= DataGridView_SelectionChanged;

                // 清除当前选择
                selectedRows.Clear();

                // 获取新选中的行（只选中第一个）
                if (dgvMineralData.SelectedRows.Count > 0)
                {
                    selectedRows.Add(dgvMineralData.SelectedRows[0].Index);
                }
                // 如果使用单元格选择，也添加到选中行
                else if (dgvMineralData.CurrentCell != null)
                {
                    selectedRows.Add(dgvMineralData.CurrentCell.RowIndex);
                }

                // 高亮显示选中行
                UpdateHighlights();

                // 在图表中高亮显示对应的点，但避免可能的递归调用
                try
                {
                    HighlightChartPoints();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"高亮显示图表点时出错: {ex.Message}");
                }

                // 输出调试信息
                if (selectedRows.Count > 0)
                {
                    int rowIndex = selectedRows.First();
                    System.Diagnostics.Debug.WriteLine($"选中行: {rowIndex}");

                    // 查找对应的数据点
                    var point = dataPoints.FirstOrDefault(p => p.RowIndex == rowIndex);
                    if (point != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"数据点: 深度={point.TopDepth:F2}, 脆性指数={point.BrittleIndex:F2}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"选择行变化时出错: {ex.Message}");
            }
            finally
            {
                // 重新绑定事件
                dgvMineralData.SelectionChanged += DataGridView_SelectionChanged;
            }
            // 不再调用SynchronizeGeoIDs方法，避免可能的循环调用
        }

        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 如果点击的是数据单元格（非列头或行头）
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // 清除当前选择
                selectedRows.Clear();

                // 只选中当前点击的行
                selectedRows.Add(e.RowIndex);

                // 更新高亮显示
                UpdateHighlights();

                // 在图表中高亮显示对应的点
                HighlightChartPoints();

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"点击数据表行: {e.RowIndex}");
            }
        }

        private void DataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            // 确保单元格格式化时保持正确的背景色
            if (e.RowIndex >= 0)
            {
                if (selectedRows.Contains(e.RowIndex))
                {
                    // 选中行使用高亮颜色
                    e.CellStyle.BackColor = Color.LightGoldenrodYellow;
                    e.CellStyle.ForeColor = Color.Black; // 确保文本颜色与背景对比度高
                    e.CellStyle.SelectionBackColor = Color.Orange; // 选中时的背景色
                    e.CellStyle.SelectionForeColor = Color.Black; // 选中时的文本颜色
                }
                else if (e.RowIndex % 2 == 0)
                {
                    // 偶数行使用深色背景
                    e.CellStyle.BackColor = Color.FromArgb(50, 50, 50);
                    e.CellStyle.ForeColor = Color.White;
                }
                else
                {
                    // 奇数行使用稍浅色背景
                    e.CellStyle.BackColor = Color.FromArgb(55, 55, 55);
                    e.CellStyle.ForeColor = Color.White;
                }

                // 为脆性指数列添加百分比符号
                if (e.Value != null && dgvMineralData.Columns[e.ColumnIndex].HeaderText.Contains("脆性指数"))
                {
                    if (double.TryParse(e.Value.ToString(), out double value))
                    {
                        e.Value = $"{value}%";
                        e.FormattingApplied = true;
                    }
                }
            }
        }

        private void UpdateHighlights()
        {
            // 强制重绘DataGridView以更新高亮显示
            dgvMineralData.Invalidate();
        }

        private void HighlightChartPoints()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"\n=== 开始高亮图表点 ===\n");
                System.Diagnostics.Debug.WriteLine($"selectedRows集合: {string.Join(", ", selectedRows)}");
                System.Diagnostics.Debug.WriteLine($"数据点总数: {dataPoints.Count}");

                // 如果有lastMatchedGeoID，优先使用它进行匹配
                if (!string.IsNullOrEmpty(lastMatchedGeoID))
                {
                    System.Diagnostics.Debug.WriteLine($"使用lastMatchedGeoID进行匹配: {lastMatchedGeoID}");
                    var matchingPoint = dataPoints.FirstOrDefault(p => p.GeoID == lastMatchedGeoID);

                    if (matchingPoint != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"使用GeoID匹配成功: GeoID={matchingPoint.GeoID}, 脆性指数={matchingPoint.BrittleIndex:F6}, 深度={matchingPoint.TopDepth:F6}");

                        // 清除所有高亮系列
                        var seriesToRemove = chartBrittleness.Series.Where(s => s.Name.StartsWith("Highlight") || s.Name.Contains("选中点")).ToList();
                        foreach (Series series in seriesToRemove)
                        {
                            chartBrittleness.Series.Remove(series);
                        }

                        // 创建新的高亮点Series
                        string seriesName = $"HighlightPoints_{DateTime.Now.Ticks}";
                        Series highlightSeries = new Series(seriesName)
                        {
                            ChartType = SeriesChartType.Point,
                            Color = Color.Gold,
                            MarkerSize = 10,
                            MarkerStyle = MarkerStyle.Circle,
                            LegendText = "选中点"
                        };

                        // 添加选中的数据点
                        highlightSeries.Points.AddXY(matchingPoint.BrittleIndex, matchingPoint.TopDepth);
                        chartBrittleness.Series.Add(highlightSeries);

                        // 更新标题
                        lblChartTitle.Text = $"选中点: 脆性指数={matchingPoint.BrittleIndex:F2}%, 深度={matchingPoint.TopDepth:F2}m";

                        // 使用BeginInvoke异步更新UI，避免可能的递归调用
                        if (!this.IsDisposed && this.IsHandleCreated)
                        {
                            this.BeginInvoke(new Action(() =>
                            {
                                if (chartBrittleness != null && !chartBrittleness.IsDisposed)
                                {
                                    chartBrittleness.Update();
                                }
                            }));
                        }

                        // 清除lastMatchedGeoID，避免影响下次匹配
                        lastMatchedGeoID = null;

                        System.Diagnostics.Debug.WriteLine($"使用GeoID高亮显示完成");
                        return;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"使用GeoID匹配失败，尝试其他方法");
                        lastMatchedGeoID = null; // 清除无效的GeoID
                    }
                }

                // 输出脆性指数为100的所有点
                var points100 = dataPoints.Where(p => Math.Abs(p.BrittleIndex - 100) < 0.01).ToList();
                System.Diagnostics.Debug.WriteLine($"脆性指数为100的点数量: {points100.Count}");
                foreach (var p in points100)
                {
                    System.Diagnostics.Debug.WriteLine($"  脆性指数100点: 深度={p.TopDepth:F2}, 行索引={p.RowIndex}");
                }

                // 首先删除所有现有的高亮点Series
                List<Series> seriesToRemove2 = new List<Series>();
                foreach (Series series in chartBrittleness.Series)
                {
                    if (series.Name.StartsWith("Highlight") || series.Name.Contains("选中点"))
                    {
                        seriesToRemove2.Add(series);
                        System.Diagnostics.Debug.WriteLine($"  将移除系列: {series.Name}");
                    }
                }

                // 删除所有需要移除的Series
                foreach (Series series in seriesToRemove2)
                {
                    chartBrittleness.Series.Remove(series);
                }
                System.Diagnostics.Debug.WriteLine($"  已移除 {seriesToRemove2.Count} 个高亮系列");

                // 如果没有选中的行，直接返回
                if (selectedRows.Count == 0)
                {
                    lblChartTitle.Text = "脆性指数曲线";
                    chartBrittleness.Invalidate();
                    System.Diagnostics.Debug.WriteLine($"  没有选中行，返回");
                    return;
                }

                // 只处理第一个选中的行（因为现在只允许选中一个点）
                int rowIndex = selectedRows.First();
                System.Diagnostics.Debug.WriteLine($"  将高亮行索引: {rowIndex}");

                // 首先尝试使用行的Tag中的GeoID进行匹配
                if (dgvMineralData.Rows.Count > rowIndex && dgvMineralData.Rows[rowIndex].Tag != null)
                {
                    string rowGeoID = dgvMineralData.Rows[rowIndex].Tag.ToString();
                    if (!string.IsNullOrEmpty(rowGeoID))
                    {
                        System.Diagnostics.Debug.WriteLine($"  从行 {rowIndex} 的Tag中获取GeoID: {rowGeoID}");
                        var matchingPoint = dataPoints.FirstOrDefault(p => p.GeoID == rowGeoID);

                        if (matchingPoint != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"  使用行Tag中的GeoID匹配成功: GeoID={matchingPoint.GeoID}, 脆性指数={matchingPoint.BrittleIndex:F6}, 行索引={matchingPoint.RowIndex}");

                            // 创建新的高亮点Series
                            string seriesName = $"HighlightPoints_{DateTime.Now.Ticks}";
                            Series highlightSeries = new Series(seriesName)
                            {
                                ChartType = SeriesChartType.Point,
                                Color = Color.Gold,
                                MarkerSize = 10,
                                MarkerStyle = MarkerStyle.Circle,
                                LegendText = "选中点"
                            };

                            // 添加选中的数据点
                            highlightSeries.Points.AddXY(matchingPoint.BrittleIndex, matchingPoint.TopDepth);
                            chartBrittleness.Series.Add(highlightSeries);
                            System.Diagnostics.Debug.WriteLine($"  添加高亮系列: {seriesName}, 脆性指数={matchingPoint.BrittleIndex:F6}, 深度={matchingPoint.TopDepth:F6}");

                            // 更新标题
                            lblChartTitle.Text = $"选中点: 脆性指数={matchingPoint.BrittleIndex:F2}%, 深度={matchingPoint.TopDepth:F2}m";

                            // 强制重绘图表
                            chartBrittleness.Invalidate();
                            System.Diagnostics.Debug.WriteLine($"  使用GeoID高亮显示完成");
                            return;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"  使用行Tag中的GeoID匹配失败: {rowGeoID}");
                        }
                    }
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"尝试高亮行索引: {rowIndex}, 数据点总数: {dataPoints.Count}");
                System.Diagnostics.Debug.WriteLine($"所有数据点:");
                foreach (var dp in dataPoints)
                {
                    System.Diagnostics.Debug.WriteLine($"  数据点: 行索引={dp.RowIndex}, 脆性指数={dp.BrittleIndex:F6}, 深度={dp.TopDepth:F6}");
                }

                // 使用更精确的方法匹配数据点
                System.Diagnostics.Debug.WriteLine($"尝试匹配行索引 {rowIndex} 的数据点");

                // 仅使用GeoID进行匹配，不使用行索引
                System.Diagnostics.Debug.WriteLine($"尝试使用GeoID匹配行索引 {rowIndex} 的数据点");

                // 输出所有数据点的GeoID信息便于调试
                foreach (var dp in dataPoints.Take(5)) // 只输出前5个点避免日志过多
                {
                    System.Diagnostics.Debug.WriteLine($"  数据点: GeoID={dp.GeoID}, 行索引={dp.RowIndex}, 脆性指数={dp.BrittleIndex:F6}");
                }

                // 如果数据表中有该行，获取该行的脆性指数和深度
                double brittleIndexFromTable = 0;
                double topDepthFromTable = 0;
                bool brittleIndexFound = false;
                bool topDepthFound = false;

                if (dgvMineralData.Rows.Count > rowIndex)
                {
                    try
                    {
                        var row = dgvMineralData.Rows[rowIndex];

                        // 尝试获取脆性指数和深度值
                        foreach (DataGridViewColumn col in dgvMineralData.Columns)
                        {
                            string headerText = col.HeaderText.Trim().ToLower();
                            if ((headerText.Contains("脆性") || headerText.Contains("脆性指数")) && row.Cells[col.Index].Value != null)
                            {
                                string cellValue = row.Cells[col.Index].Value.ToString();
                                if (double.TryParse(cellValue, out brittleIndexFromTable))
                                {
                                    brittleIndexFound = true;
                                    System.Diagnostics.Debug.WriteLine($"  从数据表中找到脆性指数: {brittleIndexFromTable}");
                                }
                            }
                            else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && row.Cells[col.Index].Value != null)
                            {
                                string cellValue = row.Cells[col.Index].Value.ToString();
                                if (double.TryParse(cellValue, out topDepthFromTable))
                                {
                                    topDepthFound = true;
                                    System.Diagnostics.Debug.WriteLine($"  从数据表中找到顶深: {topDepthFromTable}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"  获取数据表行数据时出错: {ex.Message}");
                    }
                }

                // 首先尝试使用行的Tag中的GeoID进行匹配
                DataPoint point = null;

                // 检查选中行的Tag是否包含GeoID
                if (dgvMineralData.Rows.Count > rowIndex && dgvMineralData.Rows[rowIndex].Tag != null)
                {
                    string rowGeoID = dgvMineralData.Rows[rowIndex].Tag.ToString();
                    if (!string.IsNullOrEmpty(rowGeoID))
                    {
                        System.Diagnostics.Debug.WriteLine($"  从行 {rowIndex} 的Tag中获取GeoID: {rowGeoID}");
                        point = dataPoints.FirstOrDefault(p => p.GeoID == rowGeoID);

                        if (point != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"  使用行Tag中的GeoID匹配成功: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 行索引={point.RowIndex}");
                            return;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"  使用行Tag中的GeoID匹配失败: {rowGeoID}");
                        }
                    }
                }

                // 如果使用Tag匹配失败，尝试使用脆性指数进行匹配
                if (brittleIndexFound)
                {
                    // 输出所有脆性指数接近的数据点
                    var closePoints = dataPoints.Where(p => Math.Abs(p.BrittleIndex - brittleIndexFromTable) < 0.1).ToList();
                    System.Diagnostics.Debug.WriteLine($"  脆性指数接近{brittleIndexFromTable:F6}的点数量: {closePoints.Count}");
                    foreach (var p in closePoints.Take(5))
                    {
                        System.Diagnostics.Debug.WriteLine($"    接近点: GeoID={p.GeoID}, 脆性指数={p.BrittleIndex:F6}, 行索引={p.RowIndex}, 距离={Math.Abs(p.BrittleIndex - brittleIndexFromTable):F6}");
                    }

                    // 仅使用脆性指数进行匹配
                    point = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndexFromTable) < 0.001);

                    if (point != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"  使用脆性指数精确匹配成功: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 行索引={point.RowIndex}");

                        // 将匹配到的GeoID存储到行的Tag中，便于下次匹配
                        if (dgvMineralData.Rows.Count > rowIndex)
                        {
                            dgvMineralData.Rows[rowIndex].Tag = point.GeoID;
                            System.Diagnostics.Debug.WriteLine($"  将GeoID存储到行 {rowIndex} 的Tag中: {point.GeoID}");
                        }
                    }
                }

                // 如果找不到精确匹配的行索引，尝试查找最接近的数据点
                if (point == null && dataPoints.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"未找到精确匹配的行索引 {rowIndex}，尝试查找最接近的数据点");

                    // 获取当前行的数据
                    if (dgvMineralData.Rows.Count > rowIndex)
                    {
                        try
                        {
                            var row = dgvMineralData.Rows[rowIndex];
                            double brittleIndex = 0;
                            double topDepth = 0;
                            bool foundBrittleIndex = false;
                            bool foundTopDepth = false;

                            // 输出行数据信息以进行调试
                            System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 的列数: {row.Cells.Count}");
                            foreach (DataGridViewCell cell in row.Cells)
                            {
                                if (cell.Value != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"  列 {cell.ColumnIndex} ({dgvMineralData.Columns[cell.ColumnIndex].HeaderText}): {cell.Value}");
                                }
                            }

                            // 尝试获取脆性指数和深度值
                            // 首先检查数据表中的列
                            foreach (DataGridViewColumn col in dgvMineralData.Columns)
                            {
                                string headerText = col.HeaderText.Trim().ToLower();
                                if ((headerText.Contains("脆性") || headerText.Contains("脆性指数")) && row.Cells[col.Index].Value != null)
                                {
                                    string cellValue = row.Cells[col.Index].Value.ToString();
                                    if (double.TryParse(cellValue, out brittleIndex))
                                    {
                                        foundBrittleIndex = true;
                                        System.Diagnostics.Debug.WriteLine($"  从数据表中找到脆性指数: {brittleIndex}");
                                    }
                                }
                                else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && row.Cells[col.Index].Value != null)
                                {
                                    string cellValue = row.Cells[col.Index].Value.ToString();
                                    if (double.TryParse(cellValue, out topDepth))
                                    {
                                        foundTopDepth = true;
                                        System.Diagnostics.Debug.WriteLine($"  从数据表中找到顶深: {topDepth}");
                                    }
                                }
                            }

                            // 如果没有从数据表中找到，尝试从原始数据表中获取
                            if (!foundBrittleIndex && mineralData.Rows.Count > rowIndex)
                            {
                                if (mineralData.Columns.Contains("脆性指数"))
                                {
                                    brittleIndex = Convert.ToDouble(mineralData.Rows[rowIndex]["脆性指数"]);
                                    foundBrittleIndex = true;
                                    System.Diagnostics.Debug.WriteLine($"  从原始数据表中找到脆性指数: {brittleIndex}");
                                }
                                else if (mineralData.Columns.Contains("脆性"))
                                {
                                    brittleIndex = Convert.ToDouble(mineralData.Rows[rowIndex]["脆性"]);
                                    foundBrittleIndex = true;
                                    System.Diagnostics.Debug.WriteLine($"  从原始数据表中找到脆性指数: {brittleIndex}");
                                }
                            }

                            if (!foundTopDepth && mineralData.Rows.Count > rowIndex)
                            {
                                if (mineralData.Columns.Contains("顶深"))
                                {
                                    topDepth = Convert.ToDouble(mineralData.Rows[rowIndex]["顶深"]);
                                    foundTopDepth = true;
                                    System.Diagnostics.Debug.WriteLine($"  从原始数据表中找到顶深: {topDepth}");
                                }
                                else if (mineralData.Columns.Contains("顶深/m"))
                                {
                                    topDepth = Convert.ToDouble(mineralData.Rows[rowIndex]["顶深/m"]);
                                    foundTopDepth = true;
                                    System.Diagnostics.Debug.WriteLine($"  从原始数据表中找到顶深: {topDepth}");
                                }
                            }

                            System.Diagnostics.Debug.WriteLine($"当前行数据: 脆性指数={brittleIndex:F2}(找到:{foundBrittleIndex}), 深度={topDepth:F2}(找到:{foundTopDepth})");

                            // 查找最接近的数据点
                            if (foundBrittleIndex)
                            {
                                // 仅使用脆性指数进行匹配
                                System.Diagnostics.Debug.WriteLine($"尝试使用脆性指数匹配: 脆性指数={brittleIndex:F6}");

                                // 输出所有脆性指数接近的数据点
                                var closePoints = dataPoints.Where(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.1).ToList();
                                System.Diagnostics.Debug.WriteLine($"  脆性指数接近{brittleIndex:F6}的点数量: {closePoints.Count}");
                                foreach (var p in closePoints.Take(5))
                                {
                                    System.Diagnostics.Debug.WriteLine($"    接近点: GeoID={p.GeoID}, 脆性指数={p.BrittleIndex:F6}, 行索引={p.RowIndex}, 距离={Math.Abs(p.BrittleIndex - brittleIndex):F6}");
                                }

                                // 精确匹配脆性指数
                                point = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.001);

                                if (point != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"使用脆性指数精确匹配成功: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 行索引={point.RowIndex}");
                                }
                                else
                                {
                                    // 如果精确匹配失败，尝试使用更宽松的匹配
                                    point = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.01);

                                    if (point != null)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"使用脆性指数宽松匹配成功: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 行索引={point.RowIndex}");
                                    }
                                    else
                                    {
                                        // 如果还是匹配失败，尝试找到最接近的脆性指数点
                                        point = dataPoints.OrderBy(p => Math.Abs(p.BrittleIndex - brittleIndex)).FirstOrDefault();

                                        if (point != null)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"使用最接近的脆性指数匹配成功: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 行索引={point.RowIndex}");
                                        }
                                    }
                                }
                            }
                            else if (foundTopDepth)
                            {
                                // 如果只有深度信息，只根据深度查找
                                point = dataPoints.OrderBy(p => Math.Abs(p.TopDepth - topDepth)).FirstOrDefault();

                                if (point != null)
                                    System.Diagnostics.Debug.WriteLine($"根据深度找到最接近的数据点: 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F2}, 深度={point.TopDepth:F2}");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"尝试查找最接近的数据点时出错: {ex.Message}");
                        }
                    }
                }

                if (point != null)
                {
                    // 输出匹配到的点的详细信息
                    System.Diagnostics.Debug.WriteLine($"\n找到匹配的数据点: 行索引={point.RowIndex}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");

                    // 特别处理脆性指数为100的点 - 使用更精确的匹配逻辑
                    if (Math.Abs(point.BrittleIndex - 100) < 0.01)
                    {
                        System.Diagnostics.Debug.WriteLine($"\n特别处理脆性指数为100的点");

                        // 输出所有脆性指数为100的点
                        var points100List = dataPoints.Where(p => Math.Abs(p.BrittleIndex - 100) < 0.01).ToList();
                        System.Diagnostics.Debug.WriteLine($"  脆性指数为100的点数量: {points100List.Count}");
                        foreach (var p in points100List)
                        {
                            System.Diagnostics.Debug.WriteLine($"  脆性指数100点: 行索引={p.RowIndex}, 深度={p.TopDepth:F6}, ID={p.Id}");
                        }

                        // 尝试使用数据表中的实际值进行匹配
                        if (dgvMineralData.Rows.Count > rowIndex)
                        {
                            try
                            {
                                var row = dgvMineralData.Rows[rowIndex];
                                double brittleIndex100 = 0;
                                double topDepth100 = 0;
                                bool brittleIndex100Found = false;
                                bool topDepth100Found = false;

                                // 尝试获取脆性指数和深度值
                                foreach (DataGridViewColumn col in dgvMineralData.Columns)
                                {
                                    string headerText = col.HeaderText.Trim().ToLower();
                                    if ((headerText.Contains("脆性") || headerText.Contains("脆性指数")) && row.Cells[col.Index].Value != null)
                                    {
                                        string cellValue = row.Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out brittleIndex100))
                                        {
                                            brittleIndex100Found = true;
                                            System.Diagnostics.Debug.WriteLine($"  从数据表中找到脆性指数: {brittleIndex100}");
                                        }
                                    }
                                    else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && row.Cells[col.Index].Value != null)
                                    {
                                        string cellValue = row.Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out topDepth100))
                                        {
                                            topDepth100Found = true;
                                            System.Diagnostics.Debug.WriteLine($"  从数据表中找到顶深: {topDepth100}");
                                        }
                                    }
                                }

                                // 如果找到了脆性指数和深度，尝试精确匹配
                                if (brittleIndex100Found && topDepth100Found && Math.Abs(brittleIndex100 - 100) < 0.01)
                                {
                                    // 尝试找到最接近的脆性指数为100的点
                                    var correctPoint = dataPoints
                                        .Where(p => Math.Abs(p.BrittleIndex - 100) < 0.01)
                                        .OrderBy(p => Math.Abs(p.TopDepth - topDepth100))
                                        .FirstOrDefault();

                                    if (correctPoint != null)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"  找到最接近的脆性指数100点: 行索引={correctPoint.RowIndex}, 深度={correctPoint.TopDepth:F6}");
                                        point = correctPoint;

                                        // 更新selectedRows以匹配正确的行索引
                                        selectedRows.Clear();
                                        selectedRows.Add(correctPoint.RowIndex);
                                        System.Diagnostics.Debug.WriteLine($"  更新selectedRows为 {correctPoint.RowIndex}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"  处理脆性指数为100的点时出错: {ex.Message}");
                            }
                        }
                    }

                    // 创建新的高亮点Series
                    string seriesName = $"HighlightPoints_{DateTime.Now.Ticks}";
                    Series highlightSeries = new Series(seriesName)
                    {
                        ChartType = SeriesChartType.Point,
                        Color = Color.Gold,
                        MarkerSize = 10,
                        MarkerStyle = MarkerStyle.Circle,
                        LegendText = "选中点"
                    };

                    // 添加选中的数据点
                    highlightSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                    chartBrittleness.Series.Add(highlightSeries);
                    System.Diagnostics.Debug.WriteLine($"  添加高亮系列: {seriesName}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}");

                    // 更新标题
                    lblChartTitle.Text = $"选中点: 脆性指数={point.BrittleIndex:F2}%, 深度={point.TopDepth:F2}m";

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"高亮显示点: 脆性指数={point.BrittleIndex:F2}, 深度={point.TopDepth:F2}");

                    // 更新selectedRows以匹配正确的行索引
                    if (selectedRows.Count > 0 && selectedRows[0] != point.RowIndex)
                    {
                        System.Diagnostics.Debug.WriteLine($"\n更新selectedRows: 从 {selectedRows[0]} 到 {point.RowIndex}");
                        selectedRows.Clear();
                        selectedRows.Add(point.RowIndex);
                    }
                }
                else
                {
                    lblChartTitle.Text = "脆性指数曲线";
                    System.Diagnostics.Debug.WriteLine($"\n未找到匹配的数据点");
                }

                // 强制重绘图表
                chartBrittleness.Invalidate();
                System.Diagnostics.Debug.WriteLine($"\n=== 高亮图表点完成 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示点时出错: {ex.Message}");
                MessageBox.Show($"高亮显示点时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 图表鼠标事件处理
        private Point? lastMousePosition = null;
        private bool isDragging = false;
        private bool keepScrollPosition = false; // 添加标志变量，用于跟踪是否保持滚动条位置
        private double lastViewMinY = 0; // 记录上次Y轴视图的最小值
        private double lastViewMaxY = 0; // 记录上次Y轴视图的最大值
        private const double ZOOM_FACTOR = 1.2;
        private const double MAX_ZOOM = 15.0;
        private const double MIN_ZOOM = 1.0;

        private void MineralChart_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                if (chartBrittleness == null || chartBrittleness.ChartAreas.Count == 0 || dataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];
                bool isZoomX = (ModifierKeys & Keys.Shift) == Keys.Shift; // Shift键缩放X轴
                double zoomFactor = e.Delta > 0 ? 1 / 1.2 : 1.2; // 向上滚动缩小，向下滚动放大

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"鼠标滚轮: 位置=({e.X}, {e.Y}), 滚轮值={e.Delta}, 缩放轴={(isZoomX ? "X" : "Y")}");

                if (isZoomX)
                {
                    try
                    {
                        // X轴缩放逻辑
                        double xMin = chartArea.AxisX.ScaleView.IsZoomed ? chartArea.AxisX.ScaleView.ViewMinimum : chartArea.AxisX.Minimum;
                        double xMax = chartArea.AxisX.ScaleView.IsZoomed ? chartArea.AxisX.ScaleView.ViewMaximum : chartArea.AxisX.Maximum;

                        // 防止溢出
                        if (double.IsInfinity(xMin) || double.IsNaN(xMin)) xMin = chartArea.AxisX.Minimum;
                        if (double.IsInfinity(xMax) || double.IsNaN(xMax)) xMax = chartArea.AxisX.Maximum;

                        // 确保有效范围
                        if (xMax <= xMin)
                        {
                            xMin = chartArea.AxisX.Minimum;
                            xMax = chartArea.AxisX.Maximum;
                        }

                        // 计算中心点
                        double centerX;
                        try
                        {
                            centerX = chartArea.AxisX.PixelPositionToValue(e.X);
                            if (double.IsInfinity(centerX) || double.IsNaN(centerX))
                                centerX = (xMin + xMax) / 2;
                        }
                        catch
                        {
                            centerX = (xMin + xMax) / 2;
                        }

                        // 计算新的宽度
                        double newWidth = (xMax - xMin) / zoomFactor;
                        if (newWidth <= 0.001) newWidth = 0.001; // 防止宽度过小

                        // 计算新的范围
                        double newMin = Math.Max(chartArea.AxisX.Minimum, centerX - newWidth / 2);
                        double newMax = Math.Min(chartArea.AxisX.Maximum, centerX + newWidth / 2);

                        // 确保有效范围
                        if (newMax <= newMin || double.IsInfinity(newMin) || double.IsInfinity(newMax) || double.IsNaN(newMin) || double.IsNaN(newMax))
                        {
                            newMin = chartArea.AxisX.Minimum;
                            newMax = chartArea.AxisX.Maximum;
                        }

                        // 更新缩放比例
                        double axisRange = chartArea.AxisX.Maximum - chartArea.AxisX.Minimum;
                        if (axisRange > 0 && newWidth > 0)
                            currentXZoom = axisRange / newWidth;
                        else
                            currentXZoom = 1.0;

                        currentXZoom = Math.Min(Math.Max(currentXZoom, MIN_ZOOM), MAX_ZOOM);

                        // 设置新的X轴视图范围
                        chartArea.AxisX.ScaleView.Zoom(newMin, newMax);

                        // 记录当前X轴滚动条位置
                        // 注意：虽然我们主要关注Y轴，但也记录X轴位置以保持一致性
                        keepScrollPosition = true;

                        // 输出调试信息
                        System.Diagnostics.Debug.WriteLine($"X轴缩放: 新范围=[{newMin:F2}, {newMax:F2}], 缩放比例={currentXZoom:F2}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"X轴缩放出错: {ex.Message}");
                        // 重置缩放
                        chartArea.AxisX.ScaleView.ZoomReset();
                    }
                }
                else
                {
                    try
                    {
                        // Y轴缩放逻辑
                        double yMin = chartArea.AxisY.ScaleView.IsZoomed ? chartArea.AxisY.ScaleView.ViewMinimum : chartArea.AxisY.Minimum;
                        double yMax = chartArea.AxisY.ScaleView.IsZoomed ? chartArea.AxisY.ScaleView.ViewMaximum : chartArea.AxisY.Maximum;

                        // 防止溢出
                        if (double.IsInfinity(yMin) || double.IsNaN(yMin)) yMin = chartArea.AxisY.Minimum;
                        if (double.IsInfinity(yMax) || double.IsNaN(yMax)) yMax = chartArea.AxisY.Maximum;

                        // 确保有效范围
                        if (yMax <= yMin)
                        {
                            yMin = chartArea.AxisY.Minimum;
                            yMax = chartArea.AxisY.Maximum;
                        }

                        // 计算中心点
                        double centerY;
                        try
                        {
                            centerY = chartArea.AxisY.PixelPositionToValue(e.Y);
                            if (double.IsInfinity(centerY) || double.IsNaN(centerY))
                                centerY = (yMin + yMax) / 2;
                        }
                        catch
                        {
                            centerY = (yMin + yMax) / 2;
                        }

                        // 计算新的高度
                        double newHeight = (yMax - yMin) / zoomFactor;
                        if (newHeight <= 0.001) newHeight = 0.001; // 防止高度过小

                        // 计算新的范围
                        double newMin = Math.Max(chartArea.AxisY.Minimum, centerY - newHeight / 2);
                        double newMax = Math.Min(chartArea.AxisY.Maximum, centerY + newHeight / 2);

                        // 确保有效范围
                        if (newMax <= newMin || double.IsInfinity(newMin) || double.IsInfinity(newMax) || double.IsNaN(newMin) || double.IsNaN(newMax))
                        {
                            newMin = chartArea.AxisY.Minimum;
                            newMax = chartArea.AxisY.Maximum;
                        }

                        // 更新缩放比例
                        double axisRange = chartArea.AxisY.Maximum - chartArea.AxisY.Minimum;
                        if (axisRange > 0 && newHeight > 0)
                            currentZoom = axisRange / newHeight;
                        else
                            currentZoom = 1.0;

                        currentZoom = Math.Min(Math.Max(currentZoom, MIN_ZOOM), MAX_ZOOM);

                        // 设置新的Y轴视图范围
                        chartArea.AxisY.ScaleView.Zoom(newMin, newMax);

                        // 更新Y轴刻度
                        UpdateYAxisLabels();

                        // 记录当前滚动条位置
                        lastViewMinY = newMin;
                        lastViewMaxY = newMax;
                        keepScrollPosition = true;

                        // 输出调试信息
                        System.Diagnostics.Debug.WriteLine($"Y轴缩放: 新范围=[{newMin:F2}, {newMax:F2}], 缩放比例={currentZoom:F2}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Y轴缩放出错: {ex.Message}");
                        // 重置缩放
                        chartArea.AxisY.ScaleView.ZoomReset();
                    }
                }

                // 强制重绘图表
                chartBrittleness.Invalidate();

                // 显示缩放提示
                string axisName = isZoomX ? "X轴" : "Y轴";
                double zoomValue = isZoomX ? currentXZoom : currentZoom;
                lblChartTitle.Text = $"脆性指数曲线 ({axisName}缩放: {zoomValue:F1}x)";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"鼠标滚轮事件处理出错: {ex.Message}");
                MessageBox.Show($"鼠标滚轮事件处理出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 重置缩放
                try
                {
                    if (chartBrittleness != null && chartBrittleness.ChartAreas.Count > 0)
                    {
                        chartBrittleness.ChartAreas[0].AxisX.ScaleView.ZoomReset();
                        chartBrittleness.ChartAreas[0].AxisY.ScaleView.ZoomReset();
                        currentZoom = 1.0;
                        currentXZoom = 1.0;
                    }
                }
                catch
                {
                    // 忽略重置失败
                }
            }
        }

        private void MineralChart_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                lastMousePosition = e.Location;
                isDragging = true;

                // 记录当前滚动条位置
                if (chartBrittleness != null && chartBrittleness.ChartAreas.Count > 0)
                {
                    var chartArea = chartBrittleness.ChartAreas[0];
                    if (chartArea.AxisY.ScaleView.IsZoomed)
                    {
                        lastViewMinY = chartArea.AxisY.ScaleView.ViewMinimum;
                        lastViewMaxY = chartArea.AxisY.ScaleView.ViewMaximum;
                        keepScrollPosition = true;
                        System.Diagnostics.Debug.WriteLine($"记录滚动条位置: Y轴范围=[{lastViewMinY:F2}, {lastViewMaxY:F2}]");
                    }
                }

                // 改变鼠标光标为手形
                Cursor = Cursors.Hand;
            }
        }

        private void MineralChart_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                // 处理拖动操作
                if (isDragging && lastMousePosition.HasValue && chartBrittleness != null)
                {
                    var chartArea = chartBrittleness.ChartAreas[0];

                    // 计算鼠标移动距离
                    int deltaY = lastMousePosition.Value.Y - e.Y;

                    // 如果移动距离过小，则忽略
                    if (Math.Abs(deltaY) < 2)
                        return;

                    // 计算Y轴移动比例
                    double chartHeight = chartBrittleness.Height;
                    double moveRatio = deltaY / chartHeight;

                    // 获取当前的视图范围
                    double viewMin = chartArea.AxisY.ScaleView.ViewMinimum;
                    double viewMax = chartArea.AxisY.ScaleView.ViewMaximum;
                    double viewSize = viewMax - viewMin;

                    // 计算移动距离
                    double moveAmount = viewSize * moveRatio;

                    // 计算新的视图范围
                    double newViewMin = viewMin + moveAmount;
                    double newViewMax = viewMax + moveAmount;

                    // 确保新的视图范围在数据范围内
                    if (newViewMin < chartArea.AxisY.Minimum)
                    {
                        newViewMin = chartArea.AxisY.Minimum;
                        newViewMax = newViewMin + viewSize;
                    }
                    else if (newViewMax > chartArea.AxisY.Maximum)
                    {
                        newViewMax = chartArea.AxisY.Maximum;
                        newViewMin = newViewMax - viewSize;
                    }

                    // 设置新的视图范围
                    chartArea.AxisY.ScaleView.Zoom(newViewMin, newViewMax);

                    // 更新Y轴刻度
                    UpdateYAxisLabels();

                    // 记录当前滚动条位置
                    lastViewMinY = newViewMin;
                    lastViewMaxY = newViewMax;
                    keepScrollPosition = true;

                    // 更新鼠标位置
                    lastMousePosition = e.Location;

                    // 使用BeginInvoke异步更新UI，避免可能的递归调用
                    if (!this.IsDisposed && this.IsHandleCreated)
                    {
                        this.BeginInvoke(new Action(() =>
                        {
                            if (chartBrittleness != null && !chartBrittleness.IsDisposed)
                            {
                                chartBrittleness.Update();
                            }
                        }));
                    }
                    return; // 如果正在拖动，不处理悬停效果
                }

                // 处理鼠标悬停效果
                if (chartBrittleness != null && chartBrittleness.Series.Count >= 2)
                {
                    // 获取可见矿物点系列
                    var visiblePointSeries = chartBrittleness.Series.FirstOrDefault(s => s.Name == "可见矿物点");
                    if (visiblePointSeries == null || visiblePointSeries.Points.Count == 0)
                        return;

                    // 检测鼠标是否悬停在数据点上
                    var hitTestResults = chartBrittleness.HitTest(e.X, e.Y, false, ChartElementType.DataPoint);
                    var hitResult = hitTestResults.FirstOrDefault(r => r.Series != null && r.Series.Name == "可见矿物点");

                    // 如果之前有悬停的点，先恢复其原始状态
                    if (hoveredPointIndex.HasValue && hoveredPointIndex.Value < visiblePointSeries.Points.Count)
                    {
                        try
                        {
                            var oldPoint = visiblePointSeries.Points[hoveredPointIndex.Value];
                            oldPoint.MarkerSize = originalMarkerSize;
                            oldPoint.MarkerColor = originalPointColor;
                            // 防止递归调用，直接设置边框颜色而不触发其他事件
                            oldPoint.MarkerBorderWidth = 1;
                            oldPoint.MarkerBorderColor = originalPointColor;
                            hoveredPointIndex = null;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"恢复悬停点状态时出错: {ex.Message}");
                            hoveredPointIndex = null;
                        }
                    }

                    // 如果鼠标悬停在新的数据点上
                    if (hitResult != null && hitResult.PointIndex >= 0 && hitResult.PointIndex < visiblePointSeries.Points.Count)
                    {
                        try
                        {
                            // 检查该点是否为脆性指数为100的点
                            var point = visiblePointSeries.Points[hitResult.PointIndex];
                            double brittleIndex = point.XValue;

                            // 记录当前悬停的点索引
                            hoveredPointIndex = hitResult.PointIndex;

                            // 放大并改变颜色
                            point.MarkerSize = (int)(originalMarkerSize * 1.5); // 放大1.5倍
                            point.MarkerColor = hoverPointColor;
                            // 防止递归调用，直接设置边框颜色而不触发其他事件
                            point.MarkerBorderWidth = 1;
                            point.MarkerBorderColor = hoverPointColor;

                            // 输出调试信息
                            System.Diagnostics.Trace.WriteLine($"鼠标悬停在数据点上: 索引={hitResult.PointIndex}, 脆性指数={brittleIndex:F2}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"设置悬停点状态时出错: {ex.Message}");
                            hoveredPointIndex = null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"鼠标移动事件处理出错: {ex.Message}");
            }
        }

        private void MineralChart_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isDragging = false;
                lastMousePosition = null;

                // 恢复鼠标光标为十字准星
                Cursor = Cursors.Cross;

                // 确保滚动条位置保持不变
                if (keepScrollPosition && chartBrittleness != null && chartBrittleness.ChartAreas.Count > 0)
                {
                    var chartArea = chartBrittleness.ChartAreas[0];

                    // 获取当前视图范围
                    double currentViewMin = chartArea.AxisY.ScaleView.ViewMinimum;
                    double currentViewMax = chartArea.AxisY.ScaleView.ViewMaximum;

                    // 如果当前视图范围与记录的不同，则强制设置为记录的范围
                    if (Math.Abs(currentViewMin - lastViewMinY) > 0.001 || Math.Abs(currentViewMax - lastViewMaxY) > 0.001)
                    {
                        System.Diagnostics.Debug.WriteLine($"检测到滚动条位置变化，恢复到记录的位置");
                        System.Diagnostics.Debug.WriteLine($"当前: Y轴范围=[{currentViewMin:F2}, {currentViewMax:F2}]");
                        System.Diagnostics.Debug.WriteLine($"恢复: Y轴范围=[{lastViewMinY:F2}, {lastViewMaxY:F2}]");

                        // 使用BeginInvoke异步设置，避免可能的递归调用
                        this.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                if (chartBrittleness != null && !chartBrittleness.IsDisposed && chartBrittleness.ChartAreas.Count > 0)
                                {
                                    chartArea.AxisY.ScaleView.Zoom(lastViewMinY, lastViewMaxY);
                                    UpdateYAxisLabels();
                                    chartBrittleness.Update();
                                    System.Diagnostics.Debug.WriteLine($"滚动条位置已恢复");
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"恢复滚动条位置时出错: {ex.Message}");
                            }
                        }));
                    }
                }

                // 重置标志
                keepScrollPosition = false;
            }
        }

        private void MineralChart_MouseEnter(object sender, EventArgs e)
        {
            // 鼠标进入图表区域时将光标改为十字准星
            this.Cursor = Cursors.Cross;
            chartBrittleness.Cursor = Cursors.Cross;
            System.Diagnostics.Debug.WriteLine("鼠标进入图表区域，光标改为十字准星");
        }

        private void MineralChart_MouseLeave(object sender, EventArgs e)
        {
            // 鼠标离开图表区域时恢复默认光标
            this.Cursor = Cursors.Default;
            System.Diagnostics.Debug.WriteLine("鼠标离开图表区域，光标恢复默认");

            // 恢复悬停点的原始状态
            if (chartBrittleness != null && chartBrittleness.Series.Count >= 2 && hoveredPointIndex.HasValue)
            {
                try
                {
                    var visiblePointSeries = chartBrittleness.Series.FirstOrDefault(s => s.Name == "可见矿物点");
                    if (visiblePointSeries != null && hoveredPointIndex.Value < visiblePointSeries.Points.Count)
                    {
                        var point = visiblePointSeries.Points[hoveredPointIndex.Value];
                        point.MarkerSize = originalMarkerSize;
                        point.MarkerColor = originalPointColor;
                        // 防止递归调用，直接设置边框宽度和颜色
                        point.MarkerBorderWidth = 1;
                        point.MarkerBorderColor = originalPointColor;

                        // 重置悬停点索引
                        hoveredPointIndex = null;

                        System.Diagnostics.Debug.WriteLine("恢复悬停点的原始状态");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"恢复悬停点状态时出错: {ex.Message}");
                    // 确保在出错时也重置悬停点索引
                    hoveredPointIndex = null;
                }
            }
            // 不在这里调用Invalidate()，避免可能的递归
        }

        // 键盘事件处理 - 添加上下键控制滚动功能
        private void MineralogicalForm_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // 确保图表已初始化且有数据
                if (chartBrittleness == null || chartBrittleness.ChartAreas.Count == 0 || dataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];

                // 处理上下键
                if (e.KeyCode == Keys.Up || e.KeyCode == Keys.Down)
                {
                    // 获取当前Y轴视图范围
                    double viewMin = chartArea.AxisY.ScaleView.ViewMinimum;
                    double viewMax = chartArea.AxisY.ScaleView.ViewMaximum;
                    double viewRange = viewMax - viewMin;

                    // 计算滚动步长 - 视图范围的5%
                    double scrollStep = viewRange * 0.05;

                    // 根据按键调整视图范围
                    if (e.KeyCode == Keys.Up)
                    {
                        // 向上滚动 - 深度减小
                        viewMin -= scrollStep;
                        viewMax -= scrollStep;

                        // 确保不超出数据范围
                        if (viewMin < chartArea.AxisY.Minimum)
                        {
                            double delta = chartArea.AxisY.Minimum - viewMin;
                            viewMin = chartArea.AxisY.Minimum;
                            viewMax = Math.Min(viewMax + delta, chartArea.AxisY.Maximum);
                        }
                    }
                    else // Keys.Down
                    {
                        // 向下滚动 - 深度增加
                        viewMin += scrollStep;
                        viewMax += scrollStep;

                        // 确保不超出数据范围
                        if (viewMax > chartArea.AxisY.Maximum)
                        {
                            double delta = viewMax - chartArea.AxisY.Maximum;
                            viewMax = chartArea.AxisY.Maximum;
                            viewMin = Math.Max(viewMin - delta, chartArea.AxisY.Minimum);
                        }
                    }

                    // 设置新的视图范围
                    chartArea.AxisY.ScaleView.Zoom(viewMin, viewMax);

                    // 更新Y轴刻度
                    UpdateYAxisLabels();

                    // 记录当前滚动条位置
                    lastViewMinY = viewMin;
                    lastViewMaxY = viewMax;
                    keepScrollPosition = true;

                    // 强制重绘图表
                    chartBrittleness.Invalidate();

                    // 标记事件已处理
                    e.Handled = true;
                }
                // 处理左右键
                else if (e.KeyCode == Keys.Left || e.KeyCode == Keys.Right)
                {
                    // 获取当前X轴视图范围
                    double viewMin = chartArea.AxisX.ScaleView.ViewMinimum;
                    double viewMax = chartArea.AxisX.ScaleView.ViewMaximum;
                    double viewRange = viewMax - viewMin;

                    // 计算滚动步长 - 视图范围的5%
                    double scrollStep = viewRange * 0.05;

                    // 根据按键调整视图范围
                    if (e.KeyCode == Keys.Left)
                    {
                        // 向左滚动 - 脆性指数减小
                        viewMin -= scrollStep;
                        viewMax -= scrollStep;

                        // 确保不超出数据范围
                        if (viewMin < chartArea.AxisX.Minimum)
                        {
                            double delta = chartArea.AxisX.Minimum - viewMin;
                            viewMin = chartArea.AxisX.Minimum;
                            viewMax = Math.Min(viewMax + delta, chartArea.AxisX.Maximum);
                        }
                    }
                    else // Keys.Right
                    {
                        // 向右滚动 - 脆性指数增加
                        viewMin += scrollStep;
                        viewMax += scrollStep;

                        // 确保不超出数据范围
                        if (viewMax > chartArea.AxisX.Maximum)
                        {
                            double delta = viewMax - chartArea.AxisX.Maximum;
                            viewMax = chartArea.AxisX.Maximum;
                            viewMin = Math.Max(viewMin - delta, chartArea.AxisX.Minimum);
                        }
                    }

                    // 设置新的视图范围
                    chartArea.AxisX.ScaleView.Zoom(viewMin, viewMax);

                    // 记录当前X轴滚动条位置
                    keepScrollPosition = true;

                    // 强制重绘图表
                    chartBrittleness.Invalidate();

                    // 标记事件已处理
                    e.Handled = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"键盘事件处理出错: {ex.Message}");
            }
        }

        // 将GeoID存储到匹配的行中
        private void StoreGeoIDToMatchingRows(DataPoint dataPoint)
        {
            if (dataPoint == null) return;

            string targetGeoID = dataPoint.GeoID;
            int rowIndex = dataPoint.RowIndex;

            System.Diagnostics.Trace.WriteLine($"尝试将GeoID {targetGeoID} 存储到行索引 {rowIndex} 中");

            // 首先清除所有行的Tag，避免影响后续匹配
            for (int i = 0; i < dgvMineralData.Rows.Count; i++)
            {
                if (dgvMineralData.Rows[i].Tag != null && dgvMineralData.Rows[i].Tag.ToString() == targetGeoID)
                {
                    dgvMineralData.Rows[i].Tag = null;
                    System.Diagnostics.Trace.WriteLine($"清除行 {i} 的Tag");
                }
            }

            // 首先检查行索引是否有效
            if (rowIndex >= 0 && rowIndex < dgvMineralData.Rows.Count)
            {
                // 将GeoID存储到该行的Tag中
                dgvMineralData.Rows[rowIndex].Tag = targetGeoID;
                System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {rowIndex} 的Tag中: {targetGeoID}");
                return;
            }

            // 如果行索引无效，尝试使用脆性指数和深度精确匹配
            double brittleIndex = dataPoint.BrittleIndex;
            double topDepth = dataPoint.TopDepth;
            bool foundMatch = false;
            int matchedRow = -1;

            System.Diagnostics.Trace.WriteLine($"行索引无效，尝试使用脆性指数和深度精确匹配");

            // 首先遍历所有行，找到精确匹配的行
            for (int i = 0; i < dgvMineralData.Rows.Count; i++)
            {
                try
                {
                    // 获取行中的脆性指数和深度值
                    double rowBI = 0;
                    double rowDepth = 0;
                    bool foundBI = false;
                    bool foundDepth = false;

                    foreach (DataGridViewColumn col in dgvMineralData.Columns)
                    {
                        string headerText = col.HeaderText.Trim().ToLower();
                        if ((headerText.Contains("脆性") || headerText.Contains("脆性指数")) && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                        {
                            string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                            if (double.TryParse(cellValue, out rowBI))
                            {
                                foundBI = true;
                            }
                        }
                        else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                        {
                            string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                            if (double.TryParse(cellValue, out rowDepth))
                            {
                                foundDepth = true;
                            }
                        }
                    }

                    // 仅当脆性指数和深度完全匹配时，才记录匹配的行
                    if (foundBI && foundDepth)
                    {
                        if (Math.Abs(rowBI - brittleIndex) < 0.0001 && Math.Abs(rowDepth - topDepth) < 0.0001)
                        {
                            matchedRow = i;
                            foundMatch = true;
                            System.Diagnostics.Trace.WriteLine($"找到精确匹配的行: {i}");
                            break; // 找到精确匹配后立即退出
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Trace.WriteLine($"匹配行 {i} 时出错: {ex.Message}");
                }
            }

            // 如果找到了精确匹配的行，将GeoID存储到该行的Tag中
            if (foundMatch && matchedRow >= 0 && matchedRow < dgvMineralData.Rows.Count)
            {
                dgvMineralData.Rows[matchedRow].Tag = targetGeoID;
                System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {matchedRow} 的Tag中: {targetGeoID}");
            }
            else
            {
                System.Diagnostics.Trace.WriteLine($"未找到精确匹配的行，不存储GeoID");
            }
        }

        private void MineralChart_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (chartBrittleness == null || chartBrittleness.Series.Count < 2 || dataPoints.Count == 0)
                    return;

                // 获取点击位置对应的所有数据点
                var results = chartBrittleness.HitTest(e.X, e.Y, false, ChartElementType.DataPoint);

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"点击图表: 位置=({e.X}, {e.Y}), 结果数量={results.Count()}");

                // 如果没有点击到任何点，直接返回
                if (results.Count() == 0)
                {
                    System.Diagnostics.Debug.WriteLine("未点击到任何数据点");
                    return;
                }

                // 获取点击的数据点
                var result = results.FirstOrDefault(r => r.ChartElementType == ChartElementType.DataPoint);
                if (result == null || result.PointIndex < 0 || result.Series == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到有效的数据点");
                    return;
                }

                // 获取点击的图表点的坐标
                var chartPoint = result.Series.Points[result.PointIndex];
                double brittleIndex = chartPoint.XValue;
                double topDepth = chartPoint.YValues[0];

                System.Diagnostics.Trace.WriteLine($"点击的图表点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}");

                // 输出所有数据点信息以便调试
                System.Diagnostics.Trace.WriteLine($"当前数据点总数: {dataPoints.Count}");
                System.Diagnostics.Trace.WriteLine($"点击点脆性指数: {brittleIndex:F6}, 深度: {topDepth:F6}");

                // 输出所有数据点的详细信息，便于分析问题
                System.Diagnostics.Trace.WriteLine("所有数据点详细信息:");
                foreach (var point in dataPoints.Take(20)) // 只输出前20个点避免日志过多
                {
                    System.Diagnostics.Trace.WriteLine($"  数据点: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}, 行索引={point.RowIndex}");
                }

                // 直接使用图表点的索引查找对应的数据点
                DataPoint matchingDataPoint = null;

                // 使用精确的脆性指数和深度进行匹配
                if (result.Series.Name == "矿物点" || result.Series.Name == "可见矿物点")
                {
                    System.Diagnostics.Debug.WriteLine($"点击点信息: 脆性指数={brittleIndex:F6}, 深度={topDepth:F6}");

                    // 输出所有数据点的信息便于调试
                    System.Diagnostics.Debug.WriteLine($"当前数据点总数: {dataPoints.Count}");
                    foreach (var point in dataPoints.Take(5)) // 只输出前5个点避免日志过多
                    {
                        System.Diagnostics.Debug.WriteLine($"  数据点: GeoID={point.GeoID}, 脆性指数={point.BrittleIndex:F6}, 深度={point.TopDepth:F6}, 行索引={point.RowIndex}");
                    }

                    // 使用精确的脆性指数进行匹配
                    System.Diagnostics.Trace.WriteLine($"尝试使用精确匹配找到数据点: 脆性指数={brittleIndex:F6}, 深度={topDepth:F6}");

                    // 输出所有脆性指数接近点击点的数据点
                    var closePoints = dataPoints.Where(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.1).ToList();
                    System.Diagnostics.Trace.WriteLine($"脆性指数接近{brittleIndex:F6}的点数量: {closePoints.Count}");
                    foreach (var p in closePoints)
                    {
                        System.Diagnostics.Trace.WriteLine($"  接近点: GeoID={p.GeoID}, 脆性指数={p.BrittleIndex:F6}, 深度={p.TopDepth:F6}, 行索引={p.RowIndex}, 距离={Math.Abs(p.BrittleIndex - brittleIndex):F6}");
                    }

                    // 仅使用脆性指数进行匹配，不使用深度
                    matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.001);

                    if (matchingDataPoint != null)
                    {
                        System.Diagnostics.Trace.WriteLine($"使用脆性指数精确匹配找到数据点: GeoID={matchingDataPoint.GeoID}, 脆性指数={matchingDataPoint.BrittleIndex:F6}, 深度={matchingDataPoint.TopDepth:F6}, 行索引={matchingDataPoint.RowIndex}");

                        // 记录匹配到的数据点的GeoID，便于后续在HighlightChartPoints中使用
                        lastMatchedGeoID = matchingDataPoint.GeoID;
                        System.Diagnostics.Trace.WriteLine($"记录匹配到的数据点GeoID到全局变量: {lastMatchedGeoID}");

                        // 将GeoID存储到所有匹配的行中
                        StoreGeoIDToMatchingRows(matchingDataPoint);

                        // 将GeoID存储到Tag中，便于在HighlightChartPoints中使用
                        if (selectedRows.Count > 0 && selectedRows[0] >= 0 && selectedRows[0] < dgvMineralData.Rows.Count)
                        {
                            dgvMineralData.Rows[selectedRows[0]].Tag = lastMatchedGeoID;
                            System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {selectedRows[0]} 的Tag中: {lastMatchedGeoID}");
                        }
                        else
                        {
                            System.Diagnostics.Trace.WriteLine($"无法将GeoID存储到Tag中: selectedRows为空或索引超出范围");
                        }
                    }
                    else
                    {
                        // 如果精确匹配失败，尝试使用更宽松的匹配
                        matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.01);

                        if (matchingDataPoint != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"使用脆性指数宽松匹配找到数据点: GeoID={matchingDataPoint.GeoID}, 脆性指数={matchingDataPoint.BrittleIndex:F6}, 深度={matchingDataPoint.TopDepth:F6}, 行索引={matchingDataPoint.RowIndex}");

                            // 记录匹配到的数据点的GeoID
                            lastMatchedGeoID = matchingDataPoint.GeoID;
                            System.Diagnostics.Trace.WriteLine($"记录匹配到的数据点GeoID到全局变量: {lastMatchedGeoID}");

                            // 将GeoID存储到所有匹配的行中
                            StoreGeoIDToMatchingRows(matchingDataPoint);

                            // 将GeoID存储到Tag中
                            if (selectedRows.Count > 0 && selectedRows[0] >= 0 && selectedRows[0] < dgvMineralData.Rows.Count)
                            {
                                dgvMineralData.Rows[selectedRows[0]].Tag = lastMatchedGeoID;
                                System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {selectedRows[0]} 的Tag中: {lastMatchedGeoID}");
                            }
                            else
                            {
                                System.Diagnostics.Trace.WriteLine($"无法将GeoID存储到Tag中: selectedRows为空或索引超出范围");
                            }
                        }
                        else
                        {
                            // 如果还是匹配失败，尝试找到最接近的脆性指数点
                            matchingDataPoint = dataPoints.OrderBy(p => Math.Abs(p.BrittleIndex - brittleIndex)).FirstOrDefault();

                            if (matchingDataPoint != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"使用最接近的脆性指数匹配找到数据点: GeoID={matchingDataPoint.GeoID}, 脆性指数={matchingDataPoint.BrittleIndex:F6}, 深度={matchingDataPoint.TopDepth:F6}, 行索引={matchingDataPoint.RowIndex}");

                                // 记录匹配到的数据点的GeoID
                                lastMatchedGeoID = matchingDataPoint.GeoID;
                                System.Diagnostics.Trace.WriteLine($"记录匹配到的数据点GeoID到全局变量: {lastMatchedGeoID}");

                                // 将GeoID存储到所有匹配的行中
                                StoreGeoIDToMatchingRows(matchingDataPoint);

                                // 将GeoID存储到Tag中
                                if (selectedRows.Count > 0 && selectedRows[0] >= 0 && selectedRows[0] < dgvMineralData.Rows.Count)
                                {
                                    dgvMineralData.Rows[selectedRows[0]].Tag = lastMatchedGeoID;
                                    System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {selectedRows[0]} 的Tag中: {lastMatchedGeoID}");
                                }
                                else
                                {
                                    System.Diagnostics.Trace.WriteLine($"无法将GeoID存储到Tag中: selectedRows为空或索引超出范围");
                                }
                            }
                        }
                    }
                }
                // 如果点击的是其他系列（如高亮点），尝试通过脆性指数值查找
                else if (result.Series.Name.StartsWith("Highlight") || result.Series.Name.Contains("选中点"))
                {
                    // 尝试通过脆性指数精确匹配
                    matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.01);
                    if (matchingDataPoint != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"通过脆性指数精确匹配找到数据点: 行索引={matchingDataPoint.RowIndex}, 脆性指数={matchingDataPoint.BrittleIndex:F2}");
                    }
                }

                // 如果上述方法均未找到匹配的数据点，尝试更复杂的匹配策略
                if (matchingDataPoint == null)
                {
                    // 1. 首先尝试精确匹配脆性指数和深度
                    matchingDataPoint = dataPoints.FirstOrDefault(p =>
                        Math.Abs(p.BrittleIndex - brittleIndex) < 0.001 &&
                        Math.Abs(p.TopDepth - topDepth) < 0.001);

                    if (matchingDataPoint != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"策略 1: 精确匹配成功，行索引={matchingDataPoint.RowIndex}");
                    }
                    else
                    {
                        // 2. 尝试宽松匹配脆性指数和深度
                        matchingDataPoint = dataPoints.FirstOrDefault(p =>
                            Math.Abs(p.BrittleIndex - brittleIndex) < 0.1 &&
                            Math.Abs(p.TopDepth - topDepth) < 0.1);

                        if (matchingDataPoint != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"策略 2: 宽松匹配成功，行索引={matchingDataPoint.RowIndex}");
                        }
                        else
                        {
                            // 3. 尝试只匹配脆性指数 - 对于脆性指数为100的点使用更精确的匹配
                            if (Math.Abs(brittleIndex - 100) < 0.01)
                            {
                                // 对于脆性指数为100的点，使用更严格的匹配条件
                                matchingDataPoint = dataPoints.FirstOrDefault(p =>
                                    Math.Abs(p.BrittleIndex - 100) < 0.001 &&
                                    Math.Abs(p.TopDepth - topDepth) < 0.1); // 增加深度匹配条件

                                System.Diagnostics.Debug.WriteLine($"特殊处理脆性指数为100的点，精确匹配结果(带深度): {(matchingDataPoint != null ? "成功" : "失败")}");

                                // 如果没有找到匹配点，尝试只匹配脆性指数
                                if (matchingDataPoint == null)
                                {
                                    // 输出所有脆性指数为100的点，以便调试
                                    var points100 = dataPoints.Where(p => Math.Abs(p.BrittleIndex - 100) < 0.01).ToList();
                                    System.Diagnostics.Debug.WriteLine($"脆性指数为100的点数量: {points100.Count}");
                                    foreach (var p in points100)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"脆性指数100点: 深度={p.TopDepth:F2}, 行索引={p.RowIndex}");
                                    }

                                    // 尝试找到最接近点击深度的脆性指数为100的点
                                    matchingDataPoint = dataPoints
                                        .Where(p => Math.Abs(p.BrittleIndex - 100) < 0.01)
                                        .OrderBy(p => Math.Abs(p.TopDepth - topDepth))
                                        .FirstOrDefault();

                                    System.Diagnostics.Debug.WriteLine($"特殊处理脆性指数为100的点，按深度排序匹配结果: {(matchingDataPoint != null ? $"成功，深度={matchingDataPoint.TopDepth:F2}" : "失败")}");
                                }
                            }
                            else
                            {
                                // 对于其他点，使用常规匹配
                                matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.BrittleIndex - brittleIndex) < 0.1);
                            }

                            if (matchingDataPoint != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"策略 3: 只匹配脆性指数成功，行索引={matchingDataPoint.RowIndex}");
                            }
                            else
                            {
                                // 4. 尝试只匹配深度
                                matchingDataPoint = dataPoints.FirstOrDefault(p => Math.Abs(p.TopDepth - topDepth) < 0.1);

                                if (matchingDataPoint != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"策略 4: 只匹配深度成功，行索引={matchingDataPoint.RowIndex}");
                                }
                                else
                                {
                                    // 5. 尝试找最接近的点
                                    matchingDataPoint = dataPoints.OrderBy(p =>
                                        Math.Pow(p.BrittleIndex - brittleIndex, 2) +
                                        Math.Pow(p.TopDepth - topDepth, 2)).FirstOrDefault();

                                    if (matchingDataPoint != null)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"策略 5: 找到最接近的数据点，行索引={matchingDataPoint.RowIndex}");
                                    }
                                }
                            }
                        }
                    }
                }

                // 如果找到匹配的数据点，高亮显示对应的行
                if (matchingDataPoint != null)
                {
                    // 输出匹配数据点的详细信息
                    System.Diagnostics.Debug.WriteLine($"匹配到数据点: 脆性指数={matchingDataPoint.BrittleIndex:F6}, 深度={matchingDataPoint.TopDepth:F6}, 行索引={matchingDataPoint.RowIndex}");

                    // 清除当前选择
                    selectedRows.Clear();
                    System.Diagnostics.Debug.WriteLine($"清除当前选择");

                    // 在数据表中查找匹配的GeoID
                    int actualRowIndex = -1;
                    string targetGeoID = matchingDataPoint.GeoID;

                    // 首先尝试使用行索引进行匹配
                    if (matchingDataPoint.RowIndex >= 0 && matchingDataPoint.RowIndex < dgvMineralData.Rows.Count)
                    {
                        actualRowIndex = matchingDataPoint.RowIndex;
                        System.Diagnostics.Debug.WriteLine($"使用原始行索引匹配成功: {actualRowIndex}");
                    }
                    else
                    {
                        // 如果行索引无效，尝试使用GeoID在数据表中查找匹配的行
                        System.Diagnostics.Debug.WriteLine($"尝试使用GeoID在数据表中查找匹配的行: {targetGeoID}");

                        for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                        {
                            // 检查行的Tag是否包含匹配的GeoID
                            if (dgvMineralData.Rows[i].Tag != null && dgvMineralData.Rows[i].Tag.ToString() == targetGeoID)
                            {
                                actualRowIndex = i;
                                System.Diagnostics.Debug.WriteLine($"使用Tag匹配到行: {i}");
                                break;
                            }

                            // 如果数据表中有脆性指数和深度列，尝试使用这些值进行匹配
                            if (dgvMineralData.Columns.Contains("脆性指数") && dgvMineralData.Columns.Contains("顶深/m"))
                            {
                                double tableBI = 0;
                                double tableDepth = 0;

                                if (double.TryParse(dgvMineralData.Rows[i].Cells["脆性指数"].Value?.ToString(), out tableBI) &&
                                    double.TryParse(dgvMineralData.Rows[i].Cells["顶深/m"].Value?.ToString(), out tableDepth))
                                {
                                    if (Math.Abs(tableBI - matchingDataPoint.BrittleIndex) < 0.001 &&
                                        Math.Abs(tableDepth - matchingDataPoint.TopDepth) < 0.001)
                                    {
                                        actualRowIndex = i;
                                        System.Diagnostics.Debug.WriteLine($"使用脆性指数和深度匹配到行: {i}");
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // 如果找到匹配的行，将其添加到selectedRows中
                    if (actualRowIndex >= 0)
                    {
                        selectedRows.Add(actualRowIndex);
                        System.Diagnostics.Debug.WriteLine($"添加实际行索引 {actualRowIndex} 到selectedRows集合");
                    }
                    else
                    {
                        // 如果没有找到匹配的行，使用原始的行索引
                        selectedRows.Add(matchingDataPoint.RowIndex);
                        System.Diagnostics.Debug.WriteLine($"未找到匹配行，使用原始行索引 {matchingDataPoint.RowIndex}");
                    }

                    System.Diagnostics.Debug.WriteLine($"selectedRows集合: {string.Join(", ", selectedRows)}");

                    // 更新高亮显示
                    UpdateHighlights();
                    System.Diagnostics.Debug.WriteLine($"已调用UpdateHighlights()方法");

                    // 在图表中高亮显示对应的点
                    HighlightChartPoints();
                    System.Diagnostics.Debug.WriteLine($"已调用HighlightChartPoints()方法");

                    // 选中数据表中对应的行
                    // 使用GeoID在数据表中查找匹配的行
                    int adjustedRowIndex = -1;

                    // 首先尝试在数据表中查找匹配的GeoID
                    System.Diagnostics.Trace.WriteLine($"尝试使用GeoID在数据表中查找匹配的行: {matchingDataPoint.GeoID}");

                    for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                    {
                        if (dgvMineralData.Rows[i].Tag != null && dgvMineralData.Rows[i].Tag.ToString() == matchingDataPoint.GeoID)
                        {
                            adjustedRowIndex = i;
                            System.Diagnostics.Trace.WriteLine($"使用GeoID在数据表中找到匹配的行: {i}");
                            break;
                        }
                    }

                    // 如果没有找到匹配的GeoID，尝试使用脆性指数和深度匹配
                    if (adjustedRowIndex < 0)
                    {
                        System.Diagnostics.Trace.WriteLine($"使用GeoID匹配失败，尝试使用脆性指数和深度匹配");

                        for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                        {
                            try
                            {
                                // 获取行中的脆性指数和深度值
                                double rowBI = 0;
                                double rowDepth = 0;
                                bool foundBI = false;
                                bool foundDepth = false;

                                foreach (DataGridViewColumn col in dgvMineralData.Columns)
                                {
                                    string headerText = col.HeaderText.Trim().ToLower();
                                    if ((headerText.Contains("脆性") || headerText.Contains("脆性指数")) && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                                    {
                                        string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out rowBI))
                                        {
                                            foundBI = true;
                                        }
                                    }
                                    else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                                    {
                                        string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out rowDepth))
                                        {
                                            foundDepth = true;
                                        }
                                    }
                                }

                                // 如果找到了脆性指数和深度，尝试匹配
                                if (foundBI && foundDepth)
                                {
                                    if (Math.Abs(rowBI - matchingDataPoint.BrittleIndex) < 0.001 &&
                                        Math.Abs(rowDepth - matchingDataPoint.TopDepth) < 0.001)
                                    {
                                        adjustedRowIndex = i;
                                        System.Diagnostics.Trace.WriteLine($"使用脆性指数和深度匹配到行: {i}");

                                        // 将GeoID存储到该行的Tag中
                                        // 首先清除所有行的相同GeoID
                                        for (int j = 0; j < dgvMineralData.Rows.Count; j++)
                                        {
                                            if (dgvMineralData.Rows[j].Tag != null && dgvMineralData.Rows[j].Tag.ToString() == matchingDataPoint.GeoID)
                                            {
                                                dgvMineralData.Rows[j].Tag = null;
                                                System.Diagnostics.Trace.WriteLine($"清除行 {j} 的Tag");
                                            }
                                        }

                                        // 然后将GeoID存储到匹配的行中
                                        dgvMineralData.Rows[i].Tag = matchingDataPoint.GeoID;
                                        System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {i} 的Tag中: {matchingDataPoint.GeoID}");
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Trace.WriteLine($"匹配行 {i} 时出错: {ex.Message}");
                            }
                        }
                    }

                    // 如果还是没有找到匹配的行，尝试使用脆性指数直接匹配数据表中的行
                    if (adjustedRowIndex < 0)
                    {
                        System.Diagnostics.Trace.WriteLine($"尝试使用脆性指数直接匹配数据表中的行");

                        // 输出数据表中的所有行的脆性指数值
                        System.Diagnostics.Trace.WriteLine($"数据表中的行数: {dgvMineralData.Rows.Count}");

                        // 首先输出要匹配的数据点信息
                        System.Diagnostics.Trace.WriteLine($"要匹配的数据点: 脆性指数={matchingDataPoint.BrittleIndex:F6}, 深度={matchingDataPoint.TopDepth:F6}, GeoID={matchingDataPoint.GeoID}");

                        // 创建一个列表来存储所有匹配的行
                        List<(int RowIndex, double BrittleIndex, double Depth, double Distance)> matchingRows = new List<(int, double, double, double)>();

                        for (int i = 0; i < Math.Min(dgvMineralData.Rows.Count, 50); i++)
                        {
                            try
                            {
                                // 获取行中的脆性指数值和深度值
                                double rowBI = 0;
                                double rowDepth = 0;
                                bool foundBI = false;
                                bool foundDepth = false;

                                foreach (DataGridViewColumn col in dgvMineralData.Columns)
                                {
                                    string headerText = col.HeaderText.Trim().ToLower();
                                    if (headerText.Contains("脆性") && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                                    {
                                        string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out rowBI))
                                        {
                                            foundBI = true;
                                        }
                                    }
                                    else if ((headerText.Contains("顶深") || headerText.Contains("顶深/m")) && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                                    {
                                        string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                        if (double.TryParse(cellValue, out rowDepth))
                                        {
                                            foundDepth = true;
                                        }
                                    }
                                }

                                if (foundBI)
                                {
                                    // 计算脆性指数的差异
                                    double biDiff = Math.Abs(rowBI - matchingDataPoint.BrittleIndex);

                                    // 如果找到了深度，也计算深度差异
                                    double depthDiff = foundDepth ? Math.Abs(rowDepth - matchingDataPoint.TopDepth) : 0;

                                    // 计算总距离（脆性指数差异的权重更大）
                                    double distance = biDiff * 10 + depthDiff;

                                    System.Diagnostics.Trace.WriteLine($"  行 {i}: 脆性指数 = {rowBI}, 深度 = {(foundDepth ? rowDepth.ToString() : "未找到")}, 距离 = {distance:F6}");

                                    // 添加到匹配行列表
                                    matchingRows.Add((i, rowBI, foundDepth ? rowDepth : 0, distance));
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Trace.WriteLine($"获取行 {i} 的脆性指数时出错: {ex.Message}");
                            }
                        }

                        // 按距离排序匹配的行
                        var sortedRows = matchingRows.OrderBy(r => r.Distance).ToList();

                        // 输出排序后的匹配行
                        System.Diagnostics.Trace.WriteLine($"按距离排序后的匹配行:");
                        foreach (var row in sortedRows.Take(5))
                        {
                            System.Diagnostics.Trace.WriteLine($"  行 {row.RowIndex}: 脆性指数 = {row.BrittleIndex}, 深度 = {row.Depth}, 距离 = {row.Distance:F6}");
                        }

                        // 选择最匹配的行
                        if (sortedRows.Count > 0)
                        {
                            // 首先尝试精确匹配脆性指数（允许0.01的误差）
                            var exactMatch = sortedRows.FirstOrDefault(r => Math.Abs(r.BrittleIndex - matchingDataPoint.BrittleIndex) < 0.01);

                            if (exactMatch.RowIndex >= 0)
                            {
                                adjustedRowIndex = exactMatch.RowIndex;
                                System.Diagnostics.Trace.WriteLine($"使用脆性指数精确匹配到行: {adjustedRowIndex}, 脆性指数 = {exactMatch.BrittleIndex}");
                            }
                            else
                            {
                                // 如果没有精确匹配，使用距离最小的行
                                adjustedRowIndex = sortedRows[0].RowIndex;
                                System.Diagnostics.Trace.WriteLine($"使用距离最小匹配到行: {adjustedRowIndex}, 脆性指数 = {sortedRows[0].BrittleIndex}, 距离 = {sortedRows[0].Distance:F6}");
                            }

                            // 将GeoID存储到该行的Tag中
                            dgvMineralData.Rows[adjustedRowIndex].Tag = matchingDataPoint.GeoID;
                            System.Diagnostics.Trace.WriteLine($"将GeoID存储到行 {adjustedRowIndex} 的Tag中: {matchingDataPoint.GeoID}");
                        }

                        // 如果还是没有找到匹配的行，使用原始的行索引
                        if (adjustedRowIndex < 0)
                        {
                            adjustedRowIndex = matchingDataPoint.RowIndex;
                            if (adjustedRowIndex >= dgvMineralData.Rows.Count && dgvMineralData.Rows.Count > 0)
                            {
                                // 如果索引超出范围，使用最后一行的索引
                                adjustedRowIndex = dgvMineralData.Rows.Count - 1;
                            }
                            System.Diagnostics.Trace.WriteLine($"未找到匹配行，使用调整后的行索引: {adjustedRowIndex}");
                        }
                    }

                    if (dgvMineralData.Rows.Count > 0 && adjustedRowIndex >= 0)
                    {
                        try
                        {
                            // 在选中行之前，再次验证该行的脆性指数是否与数据点匹配
                            bool isCorrectRow = false;

                            if (adjustedRowIndex >= 0 && adjustedRowIndex < dgvMineralData.Rows.Count)
                            {
                                try
                                {
                                    // 获取行中的脆性指数值
                                    double rowBI = 0;
                                    bool foundBI = false;

                                    foreach (DataGridViewColumn col in dgvMineralData.Columns)
                                    {
                                        if (col.HeaderText.Trim().ToLower().Contains("脆性") && dgvMineralData.Rows[adjustedRowIndex].Cells[col.Index].Value != null)
                                        {
                                            string cellValue = dgvMineralData.Rows[adjustedRowIndex].Cells[col.Index].Value.ToString();
                                            if (double.TryParse(cellValue, out rowBI))
                                            {
                                                foundBI = true;
                                                System.Diagnostics.Trace.WriteLine($"选中行 {adjustedRowIndex} 的脆性指数: {rowBI}");
                                                break;
                                            }
                                        }
                                    }

                                    if (foundBI)
                                    {
                                        // 如果脆性指数匹配，则确认这是正确的行
                                        if (Math.Abs(rowBI - matchingDataPoint.BrittleIndex) < 0.01)
                                        {
                                            isCorrectRow = true;
                                            System.Diagnostics.Trace.WriteLine($"确认行 {adjustedRowIndex} 的脆性指数匹配: {rowBI} 与 {matchingDataPoint.BrittleIndex:F6}");
                                        }
                                        else
                                        {
                                            System.Diagnostics.Trace.WriteLine($"警告: 行 {adjustedRowIndex} 的脆性指数不匹配: {rowBI} 与 {matchingDataPoint.BrittleIndex:F6}");

                                            // 如果不匹配，尝试再次搜索所有行找到正确的行
                                            for (int i = 0; i < dgvMineralData.Rows.Count; i++)
                                            {
                                                try
                                                {
                                                    double biValue = 0;
                                                    bool found = false;

                                                    foreach (DataGridViewColumn col in dgvMineralData.Columns)
                                                    {
                                                        if (col.HeaderText.Trim().ToLower().Contains("脆性") && dgvMineralData.Rows[i].Cells[col.Index].Value != null)
                                                        {
                                                            string cellValue = dgvMineralData.Rows[i].Cells[col.Index].Value.ToString();
                                                            if (double.TryParse(cellValue, out biValue))
                                                            {
                                                                found = true;
                                                                break;
                                                            }
                                                        }
                                                    }

                                                    if (found && Math.Abs(biValue - matchingDataPoint.BrittleIndex) < 0.01)
                                                    {
                                                        adjustedRowIndex = i;
                                                        isCorrectRow = true;
                                                        System.Diagnostics.Trace.WriteLine($"找到正确的行: {i}, 脆性指数 = {biValue}");
                                                        break;
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Trace.WriteLine($"检查行 {i} 时出错: {ex.Message}");
                                                }
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Trace.WriteLine($"验证行 {adjustedRowIndex} 时出错: {ex.Message}");
                                }
                            }

                            dgvMineralData.ClearSelection();

                            // 确保行索引在有效范围内
                            if (adjustedRowIndex >= 0 && adjustedRowIndex < dgvMineralData.Rows.Count)
                            {
                                dgvMineralData.Rows[adjustedRowIndex].Selected = true;

                                // 确保单元格索引在有效范围内
                                if (dgvMineralData.Rows[adjustedRowIndex].Cells.Count > 0)
                                {
                                    dgvMineralData.CurrentCell = dgvMineralData.Rows[adjustedRowIndex].Cells[0];
                                    dgvMineralData.FirstDisplayedScrollingRowIndex = adjustedRowIndex;
                                }
                                else
                                {
                                    System.Diagnostics.Trace.WriteLine($"行 {adjustedRowIndex} 没有单元格");
                                }
                            }
                            else
                            {
                                System.Diagnostics.Trace.WriteLine($"行索引 {adjustedRowIndex} 超出范围 (0-{dgvMineralData.Rows.Count - 1})");
                            }

                            // 强制刷新选中状态
                            selectedRows.Clear();
                            selectedRows.Add(adjustedRowIndex);
                            UpdateHighlights();

                            // 特别处理脆性指数为100的点
                            if (Math.Abs(matchingDataPoint.BrittleIndex - 100) < 0.01)
                            {
                                System.Diagnostics.Trace.WriteLine($"特别处理脆性指数为100的点，确保高亮显示");

                                // 输出当前选中状态
                                System.Diagnostics.Debug.WriteLine($"当前选中行: {string.Join(", ", selectedRows)}");
                                System.Diagnostics.Debug.WriteLine($"当前选中单元格: {dgvMineralData.CurrentCell?.RowIndex}, {dgvMineralData.CurrentCell?.ColumnIndex}");

                                // 再次强制选中行
                                dgvMineralData.ClearSelection();

                                // 确保行索引在有效范围内
                                if (adjustedRowIndex >= 0 && adjustedRowIndex < dgvMineralData.Rows.Count)
                                {
                                    dgvMineralData.Rows[adjustedRowIndex].Selected = true;

                                    // 确保单元格索引在有效范围内
                                    if (dgvMineralData.Rows[adjustedRowIndex].Cells.Count > 0)
                                    {
                                        dgvMineralData.CurrentCell = dgvMineralData.Rows[adjustedRowIndex].Cells[0];
                                    }
                                    else
                                    {
                                        System.Diagnostics.Trace.WriteLine($"行 {adjustedRowIndex} 没有单元格");
                                    }
                                }
                                else
                                {
                                    System.Diagnostics.Trace.WriteLine($"行索引 {adjustedRowIndex} 超出范围 (0-{dgvMineralData.Rows.Count - 1})");
                                }

                                // 强制刷新数据表格
                                dgvMineralData.Refresh();

                                // 再次更新高亮显示
                                UpdateHighlights();
                                HighlightChartPoints();

                                System.Diagnostics.Debug.WriteLine($"已完成脆性指数为100的点的特别处理");
                            }

                            System.Diagnostics.Trace.WriteLine($"成功选中数据表行: {adjustedRowIndex}");

                            // 无论行索引是否调整，都显示点信息
                            MessageBox.Show($"顶深: {matchingDataPoint.TopDepth}m\n底深: {matchingDataPoint.BottomDepth}m\n脆性指数: {matchingDataPoint.BrittleIndex}%", "点信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Trace.WriteLine($"选中数据表行时出错: {ex.Message}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Trace.WriteLine($"行索引 {adjustedRowIndex} 无效或数据表为空 (行数: {dgvMineralData.Rows.Count})");

                        // 即使无法选中行，也显示点信息
                        MessageBox.Show($"顶深: {matchingDataPoint.TopDepth}m\n底深: {matchingDataPoint.BottomDepth}m\n脆性指数: {matchingDataPoint.BrittleIndex}%", "点信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"选中数据点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}, 行索引={matchingDataPoint.RowIndex}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到匹配的数据点: 脆性指数={brittleIndex:F2}, 深度={topDepth:F2}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"鼠标点击事件处理出错: {ex.Message}");
                MessageBox.Show($"鼠标点击事件处理出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 同步GeoID
                SynchronizeGeoIDs();
            }
        }
        private double GetCellValueAsDouble(ICell cell)
        {
            if (cell == null) return 0;

            switch (cell.CellType)
            {
                case CellType.Numeric:
                    return cell.NumericCellValue;
                case CellType.String:
                    if (double.TryParse(cell.StringCellValue, out double result))
                        return result;
                    return 0;
                case CellType.Formula:
                    try
                    {
                        return cell.NumericCellValue;
                    }
                    catch
                    {
                        return 0;
                    }
                default:
                    return 0;
            }
        }

        private void AutoDetectColumns()
        {
            try
            {
                if (mineralData == null)
                {
                    MessageBox.Show("数据为空，请先导入数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 开始自动检测列
                DetectColumnsPosition(mineralData);

                var cleanData = ProcessRawData(mineralData);

                // 设置 DataGridView 的数据源
                dgvMineralData.DataSource = cleanData;

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"AutoDetectColumns: 处理后的数据表");
                System.Diagnostics.Debug.WriteLine($"  行数: {cleanData.Rows.Count}");
                System.Diagnostics.Debug.WriteLine($"  列名: {string.Join(", ", cleanData.Columns.Cast<DataColumn>().Select(c => c.ColumnName))}");
                System.Diagnostics.Debug.WriteLine($"  DataGridView 列数: {dgvMineralData.Columns.Count}");
                foreach (DataGridViewColumn col in dgvMineralData.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"    列: {col.Name}, 标题: {col.HeaderText}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"自动检测失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DetectColumnsPosition(DataTable table)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 开始检测列位置 ===\n");
                columnPositions.Clear();

                // 防御性检查，确保表格有效
                if (table == null || table.Columns.Count == 0 || table.Rows.Count == 0)
                {
                    throw new ArgumentException("输入数据表无效或为空");
                }

                // 记录表格基本信息
                System.Diagnostics.Debug.WriteLine($"表格列数: {table.Columns.Count}, 行数: {table.Rows.Count}");
                for (int col = 0; col < Math.Min(10, table.Columns.Count); col++)
                {
                    System.Diagnostics.Debug.WriteLine($"列 {col + 1}: {table.Columns[col].ColumnName}");
                }

                // 输出前几行数据
                System.Diagnostics.Debug.WriteLine("\n前几行数据:");
                for (int row = 0; row < Math.Min(5, table.Rows.Count); row++)
                {
                    string rowData = $"行 {row + 1}: ";
                    for (int col = 0; col < Math.Min(10, table.Columns.Count); col++)
                    {
                        rowData += $"[{table.Rows[row][col]}] ";
                    }
                    System.Diagnostics.Debug.WriteLine(rowData);
                }

                // 优先检测脆性列（包含公式）
                bool foundBrittleColumn = false;

                // 第一步：检查列名是否包含"脆性"
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    string columnName = table.Columns[col].ColumnName.Trim().ToLower();

                    // 检查列名是否精确匹配"脆性指数"
                    if (columnName.Contains("脆性指数".ToLower()))
                    {
                        // 在该列中查找包含"脆性"关键词的单元格
                        for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                        {
                            string cellText = table.Rows[row][col]?.ToString() ?? "";
                            if (cellText.Trim().ToLower().Contains("脆性".ToLower()))
                            {
                                columnPositions.Add("脆性", (row, col));
                                System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                break;
                            }
                        }

                        if (!columnPositions.ContainsKey("脆性"))
                        {
                            columnPositions.Add("脆性", (0, col)); // 如果没有找到包含关键词的单元格，默认使用第一行
                        }
                        foundBrittleColumn = true;
                        System.Diagnostics.Debug.WriteLine($"通过列名检测到脆性列: {table.Columns[col].ColumnName}");
                        break;
                    }
                }

                // 第二步：如果没有找到精确匹配脆性指数的列，则检查包含脆性关键词的列
                if (!foundBrittleColumn)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string columnName = table.Columns[col].ColumnName.Trim().ToLower();

                        // 检查列名是否包含"脆性"但不是"TOC"
                        if (columnName.Contains("脆性".ToLower()) &&
                            !columnName.Contains("toc".ToLower()) &&
                            !columnName.Contains("总有机碳".ToLower()))
                        {
                            // 在该列中查找包含"脆性"关键词的单元格
                            for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                            {
                                string cellText = table.Rows[row][col]?.ToString() ?? "";
                                if (cellText.Trim().ToLower().Contains("脆性".ToLower()))
                                {
                                    columnPositions.Add("脆性", (row, col));
                                    System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                    break;
                                }
                            }

                            if (!columnPositions.ContainsKey("脆性"))
                            {
                                columnPositions.Add("脆性", (0, col));
                            }

                            foundBrittleColumn = true;
                            System.Diagnostics.Debug.WriteLine($"通过列名包含脆性检测到脆性列: {table.Columns[col].ColumnName}");
                            break;
                        }
                    }
                }

                // 检测其他列（顶深和底深）
                for (int row = 0; row < Math.Min(config.MaxHeaderSearchRows, table.Rows.Count); row++)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string cellValue = table.Rows[row][col].ToString().Trim().ToLower();

                        // 检查顶深
                        if (cellValue.Contains("顶深".ToLower()) || cellValue.Contains("顶深度".ToLower()))
                        {
                            if (!columnPositions.ContainsKey("顶深"))
                            {
                                columnPositions.Add("顶深", (row, col));
                                System.Diagnostics.Debug.WriteLine($"检测到顶深列: 行={row + 1}, 列={col + 1}");
                            }
                        }

                        // 检查底深
                        if (cellValue.Contains("底深".ToLower()) || cellValue.Contains("底深度".ToLower()))
                        {
                            if (!columnPositions.ContainsKey("底深"))
                            {
                                columnPositions.Add("底深", (row, col));
                                System.Diagnostics.Debug.WriteLine($"检测到底深列: 行={row + 1}, 列={col + 1}");
                            }
                        }

                        // 检查脆性（如果还没有找到）
                        if (!foundBrittleColumn && (cellValue.Contains("脆性".ToLower()) || cellValue.Contains("脆性指数".ToLower())))
                        {
                            if (!columnPositions.ContainsKey("脆性"))
                            {
                                columnPositions.Add("脆性", (row, col));
                                foundBrittleColumn = true;
                                System.Diagnostics.Debug.WriteLine($"检测到脆性列: 行={row + 1}, 列={col + 1}");
                            }
                        }
                    }
                }

                // 如果没有找到顶深和底深列，尝试根据数据类型推断
                if (!columnPositions.ContainsKey("顶深") && !columnPositions.ContainsKey("底深"))
                {
                    System.Diagnostics.Debug.WriteLine("尝试根据数据类型推断顶深和底深列...");

                    // 查找前两个包含递增数字的列作为顶深和底深
                    List<int> depthColumns = new List<int>();

                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        // 跳过已识别的脆性列
                        if (foundBrittleColumn && columnPositions.ContainsKey("脆性") && columnPositions["脆性"].col == col)
                            continue;

                        // 检查是否是递增的数字列
                        bool isDepthColumn = true;
                        double lastValue = double.MinValue;
                        int numericRows = 0;

                        for (int row = 1; row < Math.Min(20, table.Rows.Count); row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            if (double.TryParse(cellValue, out double value))
                            {
                                numericRows++;
                                if (value < lastValue && lastValue != double.MinValue)
                                {
                                    // 如果数值下降，可能不是深度列
                                    isDepthColumn = false;
                                    break;
                                }
                                lastValue = value;
                            }
                        }

                        if (isDepthColumn && numericRows > 5)
                        {
                            depthColumns.Add(col);
                            System.Diagnostics.Debug.WriteLine($"列 {col + 1} 可能是深度列，包含 {numericRows} 行递增数字");

                            if (depthColumns.Count >= 2)
                                break; // 找到两个可能的深度列就停止
                        }
                    }

                    // 如果找到可能的深度列，将第一个设为顶深，第二个设为底深
                    if (depthColumns.Count >= 2)
                    {
                        columnPositions.Add("顶深", (0, depthColumns[0]));
                        columnPositions.Add("底深", (0, depthColumns[1]));
                        System.Diagnostics.Debug.WriteLine($"根据数据类型推断顶深列为列 {depthColumns[0] + 1}，底深列为列 {depthColumns[1] + 1}");
                    }
                    else if (depthColumns.Count == 1)
                    {
                        // 如果只找到一个深度列，尝试找到下一个数字列作为底深
                        columnPositions.Add("顶深", (0, depthColumns[0]));

                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            if (col == depthColumns[0] || (foundBrittleColumn && columnPositions.ContainsKey("脆性") && columnPositions["脆性"].col == col))
                                continue;

                            int numericRows = 0;
                            for (int row = 1; row < Math.Min(20, table.Rows.Count); row++)
                            {
                                string cellValue = table.Rows[row][col]?.ToString() ?? "";
                                if (double.TryParse(cellValue, out _))
                                    numericRows++;
                            }

                            if (numericRows > 5)
                            {
                                columnPositions.Add("底深", (0, col));
                                System.Diagnostics.Debug.WriteLine($"根据数据类型推断底深列为列 {col + 1}");
                                break;
                            }
                        }
                    }
                }

                // 输出检测结果
                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测结果 ===\n");
                foreach (var pos in columnPositions)
                {
                    System.Diagnostics.Debug.WriteLine($"列名: {pos.Key}, 行: {pos.Value.row + 1}, 列: {pos.Value.col + 1}");
                }

                // 处理顶深度列，将其映射为顶深
                if (columnPositions.ContainsKey("顶深度"))
                {
                    if (!columnPositions.ContainsKey("顶深"))
                    {
                        columnPositions.Add("顶深", columnPositions["顶深度"]);
                    }
                    columnPositions.Remove("顶深度");
                    System.Diagnostics.Debug.WriteLine($"将顶深度列映射为顶深并删除顶深度");
                }

                // 处理底深度列，将其映射为底深
                if (columnPositions.ContainsKey("底深度"))
                {
                    if (!columnPositions.ContainsKey("底深"))
                    {
                        columnPositions.Add("底深", columnPositions["底深度"]);
                    }
                    columnPositions.Remove("底深度");
                    System.Diagnostics.Debug.WriteLine($"将底深度列映射为底深并删除底深度");
                }

                // 检查是否至少有一个列
                if (columnPositions.Count == 0)
                {
                    string errorMsg = $"文件中没有找到任何可用的列(顶深/底深/脆性)";
                    System.Diagnostics.Debug.WriteLine($"\n错误: {errorMsg}\n");
                    throw new Exception(errorMsg);
                }

                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测完成 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n列位置检测异常: {ex.Message}\n{ex.StackTrace}\n");
                throw; // 重新抛出异常以便上层处理
            }
        }

        private DataTable ProcessRawData(DataTable rawData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 开始处理原始数据 ===\n");
                var cleanData = new DataTable();
                cleanData.Columns.Add("顶深/m", typeof(double));
                cleanData.Columns.Add("底深/m", typeof(double));
                cleanData.Columns.Add("脆性指数", typeof(double));

                int startRow = columnPositions.Values.Count > 0 ? columnPositions.Values.Max(p => p.row) + 1 : 1;
                List<double> topDepths = new List<double>();
                int validRows = 0;
                int invalidRows = 0;
                StringBuilder errorLog = new StringBuilder();

                System.Diagnostics.Debug.WriteLine($"数据开始行: {startRow + 1}");
                System.Diagnostics.Debug.WriteLine($"列位置信息:");
                foreach (var pos in columnPositions)
                {
                    System.Diagnostics.Debug.WriteLine($"  {pos.Key}: 行={pos.Value.row + 1}, 列={pos.Value.col + 1}");
                }

                for (int i = startRow; i < rawData.Rows.Count; i++)
                {
                    try
                    {
                        var row = rawData.Rows[i];

                        // 检查是否为空行
                        bool isEmptyRow = true;
                        for (int j = 0; j < rawData.Columns.Count; j++)
                        {
                            if (!string.IsNullOrWhiteSpace(row[j]?.ToString()))
                            {
                                isEmptyRow = false;
                                break;
                            }
                        }
                        if (isEmptyRow) continue;

                        // 初始化默认值
                        double top = 0.0;
                        double bottom = 0.0;
                        double brittle = 0.0;
                        bool hasValidData = false;

                        // 获取顶深值（如果存在）
                        if (columnPositions.ContainsKey("顶深"))
                        {
                            top = ParseCell(row[columnPositions["顶深"].col]);
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从顶深列获取值 {top}");
                            hasValidData = true;
                        }

                        // 获取底深值（如果存在）
                        if (columnPositions.ContainsKey("底深"))
                        {
                            bottom = ParseCell(row[columnPositions["底深"].col]);
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从底深列获取值 {bottom}");
                            hasValidData = true;
                        }

                        // 获取脆性指数值（如果存在）
                        if (columnPositions.ContainsKey("脆性"))
                        {
                            brittle = ParseCell(row[columnPositions["脆性"].col]);
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从脆性列获取值 {brittle}");
                            hasValidData = true;
                        }

                        // 只有当至少有一个有效值时才添加行
                        // 并且脆性指数不为0且不是NaN时才添加行
                        bool isBrittleValid = columnPositions.ContainsKey("脆性") ?
                            (brittle != 0 && !double.IsNaN(brittle) && !double.IsInfinity(brittle)) : true;

                        // 检查顶深和底深是否为NaN
                        bool isTopDepthValid = !double.IsNaN(top) && !double.IsInfinity(top);
                        bool isBottomDepthValid = !double.IsNaN(bottom) && !double.IsInfinity(bottom);

                        if (hasValidData && isBrittleValid && isTopDepthValid && isBottomDepthValid)
                        {
                            cleanData.Rows.Add(top, bottom, brittle);
                            topDepths.Add(top);
                            validRows++;
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 数据有效，已添加");
                        }
                        else
                        {
                            invalidRows++;
                            string reason = !hasValidData ? "没有有效值" :
                                           !isBrittleValid ? "脆性指数无效" :
                                           !isTopDepthValid ? "顶深无效" :
                                           "底深无效";

                            errorLog.AppendLine($"行 {i + 1}: 数据无效 - {reason} - 脆性指数={brittle}, 顶深={top}, 底深={bottom}");
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 数据无效 - {reason} - 脆性指数={brittle}, 顶深={top}, 底深={bottom}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 处理行时出错
                        invalidRows++;
                        errorLog.AppendLine($"行 {i + 1}: 处理错误 - {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 处理错误 - {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"\n数据处理完成: 有效行数={validRows}, 无效行数={invalidRows}\n");

                if (invalidRows > 0 && errorLog.Length > 0)
                {
                    System.Diagnostics.Debug.WriteLine("\n错误日志:\n" + errorLog.ToString());
                }

                return cleanData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n处理原始数据异常: {ex.Message}\n{ex.StackTrace}\n");
                throw; // 重新抛出异常以便上层处理
            }
        }

        private DataSet ReadExcelSheets(string path)
        {
            var dataSet = new DataSet();

            using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read))
            {
                IWorkbook workbook;
                if (path.EndsWith(".xlsx"))
                {
                    workbook = new XSSFWorkbook(fs);
                }
                else
                {
                    workbook = new HSSFWorkbook(fs);
                }
                var evaluator = workbook.GetCreationHelper().CreateFormulaEvaluator();

                for (int i = 0; i < workbook.NumberOfSheets; i++)
                {
                    var sheet = workbook.GetSheetAt(i);
                    var dataTable = new DataTable(sheet.SheetName);

                    // 读取表头
                    var headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        for (int col = 0; col < headerRow.LastCellNum; col++)
                        {
                            var cell = headerRow.GetCell(col);
                            dataTable.Columns.Add(cell?.ToString() ?? $"Column{col + 1}");
                        }
                    }

                    // 读取数据
                    for (int row = 1; row <= sheet.LastRowNum; row++)
                    {
                        var dataRow = sheet.GetRow(row);
                        if (dataRow != null)
                        {
                            var newRow = dataTable.NewRow();
                            for (int col = 0; col < dataTable.Columns.Count; col++)
                            {
                                var cell = dataRow.GetCell(col);
                                if (cell != null)
                                {
                                    // 根据单元格类型处理
                                    switch (cell.CellType)
                                    {
                                        case CellType.Formula:
                                            try
                                            {
                                                // 尝试计算公式
                                                var evaluatedCell = evaluator.Evaluate(cell);
                                                if (evaluatedCell != null)
                                                {
                                                    switch (evaluatedCell.CellType)
                                                    {
                                                        case CellType.Numeric:
                                                            newRow[col] = evaluatedCell.NumberValue.ToString();
                                                            break;
                                                        case CellType.String:
                                                            newRow[col] = evaluatedCell.StringValue;
                                                            break;
                                                        case CellType.Boolean:
                                                            newRow[col] = evaluatedCell.BooleanValue.ToString();
                                                            break;
                                                        default:
                                                            // 如果无法计算，则保留原始公式
                                                            newRow[col] = cell.CellFormula;
                                                            break;
                                                    }
                                                }
                                                else
                                                {
                                                    // 如果无法计算，则保留原始公式
                                                    newRow[col] = cell.CellFormula;
                                                }
                                            }
                                            catch
                                            {
                                                // 如果计算出错，则保留原始公式
                                                newRow[col] = cell.CellFormula;
                                            }
                                            break;
                                        case CellType.Numeric:
                                            newRow[col] = cell.NumericCellValue.ToString();
                                            break;
                                        case CellType.String:
                                            newRow[col] = cell.StringCellValue;
                                            break;
                                        case CellType.Boolean:
                                            newRow[col] = cell.BooleanCellValue.ToString();
                                            break;
                                        default:
                                            newRow[col] = cell.ToString() ?? "";
                                            break;
                                    }
                                }
                                else
                                {
                                    newRow[col] = "";
                                }
                            }
                            dataTable.Rows.Add(newRow);
                        }
                    }

                    dataSet.Tables.Add(dataTable);
                }
            }

            return dataSet;
        }

        private void pnlChart_Paint(object sender, PaintEventArgs e)
        {

        }

        /// <summary>
        /// 从AlgorithmFormulaCal加载计算的数据
        /// </summary>
        /// <param name="resultData">计算结果数据</param>
        /// <param name="brittleColumns">脆性矿物列</param>
        /// <param name="ductileColumns">塑性矿物列</param>
        public void LoadCalculatedData(DataTable resultData, List<string> brittleColumns, List<string> ductileColumns)
        {
            try
            {
                Debug.WriteLine("开始加载计算的数据，行数: " + (resultData?.Rows.Count ?? 0));

                // 确保数据表不为空
                if (resultData == null || resultData.Rows.Count == 0)
                {
                    MessageBox.Show("计算结果为空", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 初始化矿物数据表（如果需要）
                if (mineralData == null)
                {
                    mineralData = new DataTable();
                }
                else
                {
                    // 清空现有数据
                    mineralData.Clear();
                }

                // 确保矿物数据表有必要的列
                EnsureRequiredColumns(mineralData);

                // 复制计算结果数据到矿物数据表
                foreach (DataRow row in resultData.Rows)
                {
                    // 检查脆性指数是否为0，如果是则跳过该行
                    if (resultData.Columns.Contains("脆性指数") &&
                        row["脆性指数"] != DBNull.Value &&
                        Convert.ToDouble(row["脆性指数"]) == 0)
                    {
                        Debug.WriteLine("跳过脆性指数为0的行");
                        continue;
                    }

                    DataRow newRow = mineralData.NewRow();

                    // 处理顶深和底深列
                    if (resultData.Columns.Contains("顶深/m") && mineralData.Columns.Contains("顶深/m"))
                        newRow["顶深/m"] = row["顶深/m"];

                    if (resultData.Columns.Contains("底深/m") && mineralData.Columns.Contains("底深/m"))
                        newRow["底深/m"] = row["底深/m"];

                    // 处理脆性指数列
                    if (resultData.Columns.Contains("脆性指数") && mineralData.Columns.Contains("脆性指数"))
                        newRow["脆性指数"] = row["脆性指数"];

                    // 处理其他可能的列
                    foreach (DataColumn col in resultData.Columns)
                    {
                        if (!mineralData.Columns.Contains(col.ColumnName))
                            continue;

                        if (col.ColumnName != "顶深/m" && col.ColumnName != "底深/m" && col.ColumnName != "脆性指数")
                            newRow[col.ColumnName] = row[col.ColumnName];
                    }

                    mineralData.Rows.Add(newRow);
                }

                // 设置数据源
                dgvMineralData.DataSource = mineralData;

                // 设置表格属性，确保数据铺满整个面板
                dgvMineralData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

                // 设置关键列的宽度比例
                if (dgvMineralData.Columns.Contains("顶深/m") &&
                    dgvMineralData.Columns.Contains("底深/m") &&
                    dgvMineralData.Columns.Contains("脆性指数"))
                {
                    // 计算总宽度
                    int totalWidth = dgvMineralData.Width;

                    // 设置列宽比例
                    dgvMineralData.Columns["顶深/m"].Width = (int)(totalWidth * 0.2);
                    dgvMineralData.Columns["底深/m"].Width = (int)(totalWidth * 0.2);
                    dgvMineralData.Columns["脆性指数"].Width = (int)(totalWidth * 0.3);

                    // 如果有其他列，平均分配剩余宽度
                    int remainingColumns = dgvMineralData.Columns.Count - 3; // 减去顶深、底深和脆性指数列
                    if (remainingColumns > 0)
                    {
                        double remainingWidth = 0.3; // 剩余30%的宽度
                        double columnWidth = remainingWidth / remainingColumns;

                        foreach (DataGridViewColumn column in dgvMineralData.Columns)
                        {
                            if (column.Name != "顶深/m" && column.Name != "底深/m" && column.Name != "脆性指数")
                            {
                                column.Width = (int)(totalWidth * columnWidth);
                            }
                        }
                    }
                }

                // 刷新表格
                dgvMineralData.Refresh();

                // 显示脆性矿物和塑性矿物信息（如果有）
                if (brittleColumns.Count > 0 || ductileColumns.Count > 0)
                {
                    string brittleMinerals = string.Join(", ", brittleColumns.Select(c => c.Contains(":") ? c.Split(':')[0].Trim() : c));
                    string ductileMinerals = string.Join(", ", ductileColumns.Select(c => c.Contains(":") ? c.Split(':')[0].Trim() : c));

                    Debug.WriteLine($"脆性矿物: {brittleMinerals}");
                    Debug.WriteLine($"塑性矿物: {ductileMinerals}");

                    // 这里可以添加控件显示这些信息
                }

                // 自动生成曲线
                GenerateCurveFromData();

                // 显示消息
                MessageBox.Show($"已成功加载计算结果数据: {mineralData.Rows.Count} 行", "加载成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载计算数据时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"加载计算数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 确保数据表有必要的列
        /// </summary>
        /// <param name="table">要确保列的数据表</param>
        private void EnsureRequiredColumns(DataTable table)
        {
            // 检查并添加必需的列
            if (!table.Columns.Contains("顶深/m"))
                table.Columns.Add("顶深/m", typeof(double));

            if (!table.Columns.Contains("底深/m"))
                table.Columns.Add("底深/m", typeof(double));

            if (!table.Columns.Contains("脆性指数"))
                table.Columns.Add("脆性指数", typeof(double));
        }

        /// <summary>
        /// 从当前数据生成曲线
        /// </summary>
        private void GenerateCurveFromData()
        {
            try
            {
                // 检查是否有数据
                if (mineralData == null || mineralData.Rows.Count == 0)
                {
                    Debug.WriteLine("没有数据可以生成曲线");
                    return;
                }

                // 调用现有的生成曲线方法
                BtnGenerateCurve_Click(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"生成曲线时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出数据到Excel文件
        /// </summary>
        /// <param name="filePath">文件保存路径</param>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                // 创建Excel工作簿
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("脆性指数数据");

                // 创建标题行样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);

                // 获取当前数据表
                DataTable dataTable = (DataTable)dgvMineralData.DataSource;

                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);

                // 添加所有列的标题
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(dataTable.Columns[i].ColumnName);
                    cell.CellStyle = headerStyle;
                }

                // 添加数据行
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    IRow row = sheet.CreateRow(i + 1);
                    DataRow dataRow = dataTable.Rows[i];

                    // 导出所有列的数据
                    for (int j = 0; j < dataTable.Columns.Count; j++)
                    {
                        if (dataRow[j] != DBNull.Value && dataRow[j] != null)
                        {
                            try
                            {
                                // 尝试将值转换为数字
                                double numValue;
                                if (double.TryParse(dataRow[j].ToString(), out numValue))
                                {
                                    row.CreateCell(j).SetCellValue(numValue);
                                }
                                else
                                {
                                    row.CreateCell(j).SetCellValue(dataRow[j].ToString());
                                }
                            }
                            catch
                            {
                                row.CreateCell(j).SetCellValue(dataRow[j].ToString());
                            }
                        }
                        else
                        {
                            row.CreateCell(j).SetCellValue("");
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                MessageBox.Show($"成功导出 {dataTable.Rows.Count} 行数据到 {filePath}", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出Excel文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void pnlInput_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
